import React from 'react';
import Link from 'next/link';
import LogoCat from './LogoCat';
import BadgeJP from './BadgeJP';
import siteConfig from '@/data/site.json';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-matcha-900 text-cream pt-16 pb-8" style={{ backgroundColor: 'var(--color-matcha-900)', color: 'var(--color-cream)' }}>
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
          {/* Brand Column */}
          <div className="md:col-span-1">
            <div className="flex items-center gap-3 mb-4">
              <LogoCat size={32} />
              <span className="font-serif text-xl" style={{ fontWeight: 500 }}>
                The Olyn Cha
              </span>
            </div>
            <BadgeJP size="sm" className="mb-4" />
            <p className="text-sm opacity-80">
              Premium ceremonial matcha,<br />
              not-as-bitter.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-serif text-lg mb-4" style={{ fontWeight: 500 }}>Quick Links</h3>
            <nav className="flex flex-col gap-2">
              <Link href="/menu" className="text-sm hover:opacity-80 transition-opacity">Menu</Link>
              <Link href="/about" className="text-sm hover:opacity-80 transition-opacity">Our Story</Link>
              <Link href="/locations" className="text-sm hover:opacity-80 transition-opacity">Find Us</Link>
              <Link href="/contact" className="text-sm hover:opacity-80 transition-opacity">Contact</Link>
            </nav>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-serif text-lg mb-4" style={{ fontWeight: 500 }}>Visit Us</h3>
            <address className="text-sm not-italic space-y-2">
              <p>{siteConfig.location.fullAddress}</p>
              <p>
                <a href={`tel:${siteConfig.contact.phone}`} className="hover:opacity-80 transition-opacity">
                  {siteConfig.contact.phone}
                </a>
              </p>
              <p>
                <a href={`mailto:${siteConfig.contact.email}`} className="hover:opacity-80 transition-opacity">
                  {siteConfig.contact.email}
                </a>
              </p>
            </address>
          </div>

          {/* Hours */}
          <div>
            <h3 className="font-serif text-lg mb-4" style={{ fontWeight: 500 }}>Hours</h3>
            <div className="text-sm space-y-1">
              <p>Mon-Thu: 9:00 AM - 6:00 PM</p>
              <p>Fri-Sat: 9:00 AM - 7:00 PM</p>
              <p>Sunday: 9:00 AM - 6:00 PM</p>
            </div>
          </div>
        </div>

        {/* Social Links */}
        <div className="border-t border-cream/20 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex gap-6">
              <a href={`https://instagram.com/${siteConfig.social.instagram.replace('@', '')}`} 
                 target="_blank" 
                 rel="noopener noreferrer"
                 className="hover:opacity-80 transition-opacity"
                 aria-label="Instagram">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zM5.838 12a6.162 6.162 0 1 1 12.324 0 6.162 6.162 0 0 1-12.324 0zM12 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm4.965-10.405a1.44 1.44 0 1 1 2.881.001 1.44 1.44 0 0 1-2.881-.001z"/>
                </svg>
              </a>
              <a href={`https://facebook.com/${siteConfig.social.facebook}`} 
                 target="_blank" 
                 rel="noopener noreferrer"
                 className="hover:opacity-80 transition-opacity"
                 aria-label="Facebook">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href={`https://tiktok.com/${siteConfig.social.tiktok.replace('@', '')}`} 
                 target="_blank" 
                 rel="noopener noreferrer"
                 className="hover:opacity-80 transition-opacity"
                 aria-label="TikTok">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                </svg>
              </a>
            </div>

            <div className="flex flex-col md:flex-row items-center gap-4 text-sm">
              <div className="flex gap-4">
                <Link href="/contact" className="hover:opacity-80 transition-opacity">Privacy</Link>
                <Link href="/contact" className="hover:opacity-80 transition-opacity">Terms</Link>
              </div>
              <p className="opacity-60">© {currentYear} The Olyn Cha. All rights reserved.</p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

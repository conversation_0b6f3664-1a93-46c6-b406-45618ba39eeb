@import url("https://fonts.googleapis.com/css2?family=Jost:wght@400;500;600&family=Playfair+Display:wght@600;700&display=swap");

.productSection {
  padding: 5rem 0;
  background: #f0f5f1;
}

.container {
  width: min(1120px, calc(100% - 3rem));
  margin: 0 auto;
}

.grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.card {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border: none;
  border-radius: 1.75rem;
  overflow: hidden;
  box-shadow: 0 28px 60px rgba(32, 62, 43, 0.12);
  transition: transform 260ms ease, box-shadow 260ms ease;
}

.cardContent {
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card:hover {
  transform: translateY(-6px);
  box-shadow: 0 34px 70px rgba(32, 62, 43, 0.18);
}

.imageWrap {
  position: relative;
  overflow: hidden;
  isolation: isolate;
}

.productImage {
  width: 100%;
  height: 260px;
  object-fit: cover;
  transition: transform 380ms ease;
}

.card:hover .productImage {
  transform: scale(1.05);
}

.categoryBadge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: #3d643b;
  color: #ffffff;
  padding: 0.4rem 1rem;
  border-radius: 999px;
  font-family: "Jost", sans-serif;
  font-size: 0.75rem;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  font-weight: 600;
  box-shadow: 0 8px 18px rgba(61, 100, 59, 0.26);
}

.content {
  padding: 1.75rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  flex: 1;
}

.headingGroup {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

.productName {
  font-family: "Playfair Display", serif;
  font-size: 1.5rem;
  line-height: 1.3;
  color: #1f2f24;
  letter-spacing: 0.01em;
}

.productDescription {
  font-family: "Jost", sans-serif;
  font-size: 0.95rem;
  line-height: 1.65;
  color: #5b6d5d;
}

.footer {
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price {
  font-family: "Playfair Display", serif;
  font-size: 1.6rem;
  color: #1f2f24;
}

.cta {
  background: #3d643b;
  color: #ffffff;
  font-family: "Jost", sans-serif;
  font-weight: 600;
  border-radius: 999px;
  padding: 0.6rem 1.8rem;
  transition: background 220ms ease, transform 220ms ease, box-shadow 220ms ease;
}

.cta:hover {
  background: #335233;
  transform: translateY(-1px);
  box-shadow: 0 16px 30px rgba(61, 100, 59, 0.25);
}

@media (max-width: 640px) {
  .productSection {
    padding: 3.5rem 0;
  }

  .content {
    padding: 1.5rem;
  }

  .productName {
    font-size: 1.35rem;
  }

  .productImage {
    height: 220px;
  }
}

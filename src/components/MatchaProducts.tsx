import { Card, CardContent } from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import styles from "./MatchaProducts.module.css";

const products = [
  {
    id: 1,
    name: "Premium Matcha Set",
    description: "Traditional bamboo whisk with ceramic bowl",
    price: "$25",
    image: "https://images.unsplash.com/photo-1755685682321-d4a38aa26214?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxiYW1ib28lMjBtYXRjaGElMjB3aGlzayUyMGNoYXNlbnxlbnwxfHx8fDE3NTY2NjUyNTJ8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral",
    category: "Matcha Mug"
  },
  {
    id: 2,
    name: "Ceremonial Grade",
    description: "Authentic Japanese matcha powder",
    price: "$35",
    image: "https://images.unsplash.com/photo-1643185720431-9c050eebbc9a?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxtYXRjaGElMjBwb3dkZXIlMjBib3dsJTIwYmFtYm9vfGVufDF8fHx8MTc1NjY2NTI0Nnww&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral",
    category: "Organic Matcha"
  },
  {
    id: 3,
    name: "Artisan Tea Bowl",
    description: "Handcrafted ceramic matcha bowl",
    price: "$45",
    image: "https://images.unsplash.com/photo-1672354198778-b9da86b06d99?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxtYXRjaGElMjB0ZWElMjBjZXJlbW9ueSUyMHdvb2RlbnxlbnwxfHx8fDE3NTY2NjUyNDl8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral",
    category: "Ceramic Bowl"
  }
];

export default function MatchaProducts() {
  return (
    <section className={styles.productSection}>
      <div className={styles.container}>
        <div className={styles.grid}>
          {products.map((product) => (
            <Card key={product.id} className={styles.card}>
              <CardContent className={styles.cardContent}>
                <div className={styles.imageWrap}>
                  <ImageWithFallback
                    src={product.image}
                    alt={product.name}
                    className={styles.productImage}
                  />
                  <div className={styles.categoryBadge}>{product.category}</div>
                </div>

                <div className={styles.content}>
                  <div className={styles.headingGroup}>
                    <h3 className={styles.productName}>{product.name}</h3>
                    <p className={styles.productDescription}>{product.description}</p>
                  </div>

                  <div className={styles.footer}>
                    <div className={styles.price}>{product.price}</div>
                    <Button size="sm" className={styles.cta}>
                      Add to Cart
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

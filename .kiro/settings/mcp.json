{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": false, "autoApprove": ["fetch"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}, "disabled": false, "autoApprove": ["sequentialthinking"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {"FILESYSTEM_ALLOWED_PATHS": "/Users/<USER>/Documents/project/olyncha"}, "disabled": false, "autoApprove": ["read_file", "list_directory"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "env": {}, "disabled": false, "autoApprove": ["browser_resize", "browser_navigate", "browser_take_screenshot", "browser_click", "browser_evaluate", "browser_type", "browser_scroll", "browser_wait_for_selector"]}}}
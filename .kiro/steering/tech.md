# Technology Stack

## Framework & Runtime
- **Next.js 15** with App Router architecture
- **React 18** with TypeScript
- **Node.js** runtime environment

## Styling & UI
- **Tailwind CSS 4.1.3** for styling (custom CSS variables approach)
- **Radix UI** primitives for accessible components
- **Heroicons** for iconography
- **Custom CSS** with modern design tokens and animations

## State Management & Context
- **React Context** + useReducer for global state (Cart, Auth)
- **Local Storage** for cart persistence
- **Custom hooks** for reusable logic

## Backend & Services
- **Firebase** ecosystem:
  - Authentication (Email/Password, Google, Apple)
  - Firestore database
  - Cloud Storage
  - Cloud Functions
- **Stripe** for payment processing

## Development & Testing
- **TypeScript** for type safety
- **Jest** + React Testing Library for unit/integration tests
- **Playwright** for E2E testing
- **ESLint** for code quality

## Build & Deployment
- **Vercel** for hosting and deployment
- **npm** as package manager
- **GitHub Actions** for CI/CD

## Common Commands

```bash
# Development
npm run dev              # Start dev server on port 3000
npm run dev:vite        # Alternative Vite dev server

# Building
npm run build           # Production build
npm run start           # Start production server

# Testing
npm run test            # Run Jest unit tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Generate coverage report
npm run test:ci         # Run tests for CI/CD

# Code Quality
npm run lint            # Run ESLint
```

## Key Dependencies
- **UI Components**: @radix-ui/react-*, @heroicons/react
- **Styling**: tailwindcss, class-variance-authority, clsx, tailwind-merge
- **Forms**: react-hook-form
- **Animations**: Custom CSS animations, Three.js integration
- **Utils**: lucide-react, next-themes, sonner (toasts)
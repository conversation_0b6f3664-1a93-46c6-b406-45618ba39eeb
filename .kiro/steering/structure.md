# Project Structure & Organization

## Next.js App Router Structure
```
app/                    # Next.js 15 App Router
├── (auth)/            # Route groups for auth pages
├── api/               # API routes
│   ├── auth/          # Authentication endpoints
│   ├── orders/        # Order management APIs
│   └── products/      # Product catalog APIs
├── [feature]/         # Feature-based page organization
├── globals.css        # Global styles with CSS variables
└── layout.tsx         # Root layout with providers
```

## Component Architecture
```
components/            # Reusable UI components
├── ui/               # Base UI components (Radix-based)
├── [FeatureName].tsx # Feature-specific components
└── [FeatureName]Modern.tsx # Modern variants
```

## State Management
```
contexts/             # React Context providers
├── AuthContext.tsx   # Authentication state
├── CartContext.tsx   # Shopping cart state
└── FirebaseAuthContext.tsx # Firebase auth integration
```

## Data & Configuration
```
data/                 # Static data and configuration
├── menu.json         # Product catalog
├── products.json     # Product definitions
├── products.ts       # Product utilities
└── site.json         # Site configuration
```

## Testing Strategy
```
__tests__/            # Test files organized by type
├── api/              # API route tests
├── components/       # Component tests
├── contexts/         # Context provider tests
├── e2e/              # End-to-end tests
├── pages/            # Page component tests
└── utils/            # Utility function tests
```

## Naming Conventions

### Files & Folders
- **Components**: PascalCase (e.g., `HeaderModern.tsx`)
- **Pages**: lowercase with hyphens (e.g., `order-status/`)
- **API Routes**: lowercase with hyphens (e.g., `api/orders/[id]/`)
- **Contexts**: PascalCase with "Context" suffix
- **Types**: PascalCase in `types/index.ts`

### Code Conventions
- **React Components**: PascalCase with descriptive names
- **Hooks**: camelCase starting with "use"
- **Constants**: UPPER_SNAKE_CASE
- **CSS Classes**: Tailwind utility classes + custom CSS variables
- **API Endpoints**: RESTful naming (GET, POST, PUT, DELETE)

## Import Organization
```typescript
// 1. React and Next.js imports
import React from 'react';
import Link from 'next/link';

// 2. Third-party libraries
import { ShoppingBagIcon } from '@heroicons/react/24/outline';

// 3. Internal imports (using @ alias)
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
```

## CSS Architecture
- **Global styles**: `app/globals.css` with CSS custom properties
- **Component styles**: Tailwind utility classes
- **Design tokens**: CSS variables for colors, spacing, typography
- **Animations**: Custom keyframes and utility classes
- **Responsive**: Mobile-first approach with Tailwind breakpoints

## Key Patterns
- **Context + Reducer**: For complex state management
- **Custom Hooks**: For reusable logic extraction
- **Component Composition**: Radix UI + custom styling
- **Type Safety**: Comprehensive TypeScript interfaces
- **Error Boundaries**: Graceful error handling
- **Loading States**: Skeleton components and loading indicators
<html>
    <head>
        <link
            rel="preconnect"
            href="https://fonts.gstatic.com/"
            crossorigin=""
        />
        <link
            rel="stylesheet"
            as="style"
            onload="this.rel='stylesheet'"
            href="https://fonts.googleapis.com/css2?display=swap&amp;family=Epilogue%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
        />

        <title>Stitch Design</title>
        <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

        <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    </head>
    <body>
        <div
            class="relative flex size-full min-h-screen flex-col bg-[#f9fcf8] group/design-root overflow-x-hidden"
            style="font-family: Epilogue, &quot;Noto Sans&quot;, sans-serif"
        >
            <div class="layout-container flex h-full grow flex-col">
                <header
                    class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#ebf3e7] px-10 py-3"
                >
                    <div class="flex items-center gap-4 text-[#121b0e]">
                        <div class="size-4">
                            <svg
                                viewBox="0 0 48 48"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <g clip-path="url(#clip0_6_535)">
                                    <path
                                        fill-rule="evenodd"
                                        clip-rule="evenodd"
                                        d="M47.2426 24L24 47.2426L0.757355 24L24 0.757355L47.2426 24ZM12.2426 21H35.7574L24 9.24264L12.2426 21Z"
                                        fill="currentColor"
                                    ></path>
                                </g>
                                <defs>
                                    <clipPath id="clip0_6_535">
                                        <rect
                                            width="48"
                                            height="48"
                                            fill="white"
                                        ></rect>
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <h2
                            class="text-[#121b0e] text-lg font-bold leading-tight tracking-[-0.015em]"
                        >
                            Matcha Cafe
                        </h2>
                    </div>
                    <div class="flex flex-1 justify-end gap-8">
                        <div class="flex items-center gap-9">
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Menu</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Catering</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Gift Cards</a
                            >
                        </div>
                        <div class="flex gap-2">
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#ebf3e7] text-[#121b0e] text-sm font-bold leading-normal tracking-[0.015em]"
                            >
                                <span class="truncate">Order Pickup</span>
                            </button>
                            <button
                                class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 bg-[#ebf3e7] text-[#121b0e] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
                            >
                                <div
                                    class="text-[#121b0e]"
                                    data-icon="MagnifyingGlass"
                                    data-size="20px"
                                    data-weight="regular"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="20px"
                                        height="20px"
                                        fill="currentColor"
                                        viewBox="0 0 256 256"
                                    >
                                        <path
                                            d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                                        ></path>
                                    </svg>
                                </div>
                            </button>
                            <button
                                class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 bg-[#ebf3e7] text-[#121b0e] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
                            >
                                <div
                                    class="text-[#121b0e]"
                                    data-icon="ShoppingCart"
                                    data-size="20px"
                                    data-weight="regular"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="20px"
                                        height="20px"
                                        fill="currentColor"
                                        viewBox="0 0 256 256"
                                    >
                                        <path
                                            d="M222.14,58.87A8,8,0,0,0,216,56H54.68L49.79,29.14A16,16,0,0,0,34.05,16H16a8,8,0,0,0,0,16h18L59.56,172.29a24,24,0,0,0,5.33,11.27,28,28,0,1,0,44.4,8.44h45.42A27.75,27.75,0,0,0,152,204a28,28,0,1,0,28-28H83.17a8,8,0,0,1-7.87-6.57L72.13,152h116a24,24,0,0,0,23.61-19.71l12.16-66.86A8,8,0,0,0,222.14,58.87ZM96,204a12,12,0,1,1-12-12A12,12,0,0,1,96,204Zm96,0a12,12,0,1,1-12-12A12,12,0,0,1,192,204Zm4-74.57A8,8,0,0,1,188.1,136H69.22L57.59,72H206.41Z"
                                        ></path>
                                    </svg>
                                </div>
                            </button>
                        </div>
                    </div>
                </header>
                <div class="px-40 flex flex-1 justify-center py-5">
                    <div
                        class="layout-content-container flex flex-col max-w-[960px] flex-1"
                    >
                        <div class="flex flex-wrap justify-between gap-3 p-4">
                            <div class="flex min-w-72 flex-col gap-3">
                                <p
                                    class="text-[#121b0e] tracking-light text-[32px] font-bold leading-tight"
                                >
                                    Order Status
                                </p>
                                <p
                                    class="text-[#67974e] text-sm font-normal leading-normal"
                                >
                                    Order #123456789
                                </p>
                            </div>
                        </div>
                        <div class="flex flex-col gap-3 p-4">
                            <div class="flex gap-6 justify-between">
                                <p
                                    class="text-[#121b0e] text-base font-medium leading-normal"
                                >
                                    Order Placed
                                </p>
                            </div>
                            <div class="rounded bg-[#d7e7d0]">
                                <div
                                    class="h-2 rounded bg-[#4bb814]"
                                    style="width: 25%"
                                ></div>
                            </div>
                            <p
                                class="text-[#67974e] text-sm font-normal leading-normal"
                            >
                                10:00 AM
                            </p>
                        </div>
                        <div class="flex flex-col gap-3 p-4">
                            <div class="flex gap-6 justify-between">
                                <p
                                    class="text-[#121b0e] text-base font-medium leading-normal"
                                >
                                    Preparing
                                </p>
                            </div>
                            <div class="rounded bg-[#d7e7d0]">
                                <div
                                    class="h-2 rounded bg-[#4bb814]"
                                    style="width: 50%"
                                ></div>
                            </div>
                            <p
                                class="text-[#67974e] text-sm font-normal leading-normal"
                            >
                                10:15 AM
                            </p>
                        </div>
                        <div class="flex flex-col gap-3 p-4">
                            <div class="flex gap-6 justify-between">
                                <p
                                    class="text-[#121b0e] text-base font-medium leading-normal"
                                >
                                    Out for Delivery
                                </p>
                            </div>
                            <div class="rounded bg-[#d7e7d0]">
                                <div
                                    class="h-2 rounded bg-[#4bb814]"
                                    style="width: 75%"
                                ></div>
                            </div>
                            <p
                                class="text-[#67974e] text-sm font-normal leading-normal"
                            >
                                10:30 AM
                            </p>
                        </div>
                        <div class="flex flex-col gap-3 p-4">
                            <div class="flex gap-6 justify-between">
                                <p
                                    class="text-[#121b0e] text-base font-medium leading-normal"
                                >
                                    Delivered
                                </p>
                            </div>
                            <div class="rounded bg-[#d7e7d0]">
                                <div
                                    class="h-2 rounded bg-[#4bb814]"
                                    style="width: 100%"
                                ></div>
                            </div>
                            <p
                                class="text-[#67974e] text-sm font-normal leading-normal"
                            >
                                10:45 AM
                            </p>
                        </div>
                        <h2
                            class="text-[#121b0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5"
                        >
                            Your Order
                        </h2>
                        <div
                            class="flex items-center gap-4 bg-[#f9fcf8] px-4 min-h-[72px] py-2 justify-between"
                        >
                            <div class="flex flex-col justify-center">
                                <p
                                    class="text-[#121b0e] text-base font-medium leading-normal line-clamp-1"
                                >
                                    1 x Matcha Latte
                                </p>
                                <p
                                    class="text-[#67974e] text-sm font-normal leading-normal line-clamp-2"
                                >
                                    Matcha Latte
                                </p>
                            </div>
                            <div class="shrink-0">
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    $5.00
                                </p>
                            </div>
                        </div>
                        <div
                            class="flex items-center gap-4 bg-[#f9fcf8] px-4 min-h-[72px] py-2 justify-between"
                        >
                            <div class="flex flex-col justify-center">
                                <p
                                    class="text-[#121b0e] text-base font-medium leading-normal line-clamp-1"
                                >
                                    1 x Matcha Ice Cream
                                </p>
                                <p
                                    class="text-[#67974e] text-sm font-normal leading-normal line-clamp-2"
                                >
                                    Matcha Ice Cream
                                </p>
                            </div>
                            <div class="shrink-0">
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    $6.00
                                </p>
                            </div>
                        </div>
                        <div
                            class="flex items-center gap-4 bg-[#f9fcf8] px-4 min-h-[72px] py-2 justify-between"
                        >
                            <div class="flex flex-col justify-center">
                                <p
                                    class="text-[#121b0e] text-base font-medium leading-normal line-clamp-1"
                                >
                                    1 x Matcha Cake
                                </p>
                                <p
                                    class="text-[#67974e] text-sm font-normal leading-normal line-clamp-2"
                                >
                                    Matcha Cake
                                </p>
                            </div>
                            <div class="shrink-0">
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    $7.00
                                </p>
                            </div>
                        </div>
                        <div
                            class="flex items-center gap-4 bg-[#f9fcf8] px-4 min-h-14 justify-between"
                        >
                            <p
                                class="text-[#121b0e] text-base font-normal leading-normal flex-1 truncate"
                            >
                                Subtotal
                            </p>
                            <div class="shrink-0">
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    $18.00
                                </p>
                            </div>
                        </div>
                        <div
                            class="flex items-center gap-4 bg-[#f9fcf8] px-4 min-h-14 justify-between"
                        >
                            <p
                                class="text-[#121b0e] text-base font-normal leading-normal flex-1 truncate"
                            >
                                Delivery Fee
                            </p>
                            <div class="shrink-0">
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    $2.00
                                </p>
                            </div>
                        </div>
                        <div
                            class="flex items-center gap-4 bg-[#f9fcf8] px-4 min-h-14 justify-between"
                        >
                            <p
                                class="text-[#121b0e] text-base font-normal leading-normal flex-1 truncate"
                            >
                                Tax
                            </p>
                            <div class="shrink-0">
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    $1.50
                                </p>
                            </div>
                        </div>
                        <div
                            class="flex items-center gap-4 bg-[#f9fcf8] px-4 min-h-14 justify-between"
                        >
                            <p
                                class="text-[#121b0e] text-base font-normal leading-normal flex-1 truncate"
                            >
                                Total
                            </p>
                            <div class="shrink-0">
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    $21.50
                                </p>
                            </div>
                        </div>
                        <div class="flex px-4 py-3 justify-end">
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#ebf3e7] text-[#121b0e] text-sm font-bold leading-normal tracking-[0.015em]"
                            >
                                <span class="truncate">View Order Details</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>

<html>
    <head>
        <link
            rel="preconnect"
            href="https://fonts.gstatic.com/"
            crossorigin=""
        />
        <link
            rel="stylesheet"
            as="style"
            onload="this.rel='stylesheet'"
            href="https://fonts.googleapis.com/css2?display=swap&amp;family=Epilogue%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
        />

        <title>Stitch Design</title>
        <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

        <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    </head>
    <body>
        <div
            class="relative flex size-full min-h-screen flex-col bg-[#f9fcf8] group/design-root overflow-x-hidden"
            style="font-family: Epilogue, &quot;Noto Sans&quot;, sans-serif"
        >
            <div class="layout-container flex h-full grow flex-col">
                <header
                    class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#ebf3e7] px-10 py-3"
                >
                    <div class="flex items-center gap-4 text-[#121b0e]">
                        <div class="size-4">
                            <svg
                                viewBox="0 0 48 48"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <g clip-path="url(#clip0_6_535)">
                                    <path
                                        fill-rule="evenodd"
                                        clip-rule="evenodd"
                                        d="M47.2426 24L24 47.2426L0.757355 24L24 0.757355L47.2426 24ZM12.2426 21H35.7574L24 9.24264L12.2426 21Z"
                                        fill="currentColor"
                                    ></path>
                                </g>
                                <defs>
                                    <clipPath id="clip0_6_535">
                                        <rect
                                            width="48"
                                            height="48"
                                            fill="white"
                                        ></rect>
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <h2
                            class="text-[#121b0e] text-lg font-bold leading-tight tracking-[-0.015em]"
                        >
                            Matcha Cafe
                        </h2>
                    </div>
                    <div class="flex flex-1 justify-end gap-8">
                        <div class="flex items-center gap-9">
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Menu</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Locations</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >About</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Contact</a
                            >
                        </div>
                        <button
                            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#4bb814] text-[#121b0e] text-sm font-bold leading-normal tracking-[0.015em]"
                        >
                            <span class="truncate">Order Online</span>
                        </button>
                    </div>
                </header>
                <div class="px-40 flex flex-1 justify-center py-5">
                    <div
                        class="layout-content-container flex flex-col max-w-[960px] flex-1"
                    >
                        <div class="@container">
                            <div class="@[480px]:p-4">
                                <div
                                    class="flex min-h-[480px] flex-col gap-6 bg-cover bg-center bg-no-repeat @[480px]:gap-8 @[480px]:rounded-xl items-center justify-center p-4"
                                    style="
                                        background-image:
                                            linear-gradient(
                                                rgba(0, 0, 0, 0.1) 0%,
                                                rgba(0, 0, 0, 0.4) 100%
                                            ),
                                            url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuC0PyHKh1gW0yI4dtKcQAdcgspiAAgJOXYeMwrYAz27Qyg0siaAhSiefl-_Tke39vLlO4K4omHPNwEq3_9oymz1Xu1vejqj9PcoVv97GrLZPw0IMB8XEmdaVUiIAkZ0wqp-NxAh5cDx44Cwd2H-3xPRox0F9wtq0xIIvcdbmq5xOT8oOs5dt1Ua-AIpWYG7GX4wH58l74fuyuC6x72C147MO1-RV05RS5NQLIM7CYpKqijM65HGZXj98TKTeve98WWPrNpawrKsugQ&quot;);
                                    "
                                >
                                    <div
                                        class="flex flex-col gap-2 text-center"
                                    >
                                        <h1
                                            class="text-white text-4xl font-black leading-tight tracking-[-0.033em] @[480px]:text-5xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em]"
                                        >
                                            Savor the Essence of Matcha
                                        </h1>
                                        <h2
                                            class="text-white text-sm font-normal leading-normal @[480px]:text-base @[480px]:font-normal @[480px]:leading-normal"
                                        >
                                            Discover our exquisite range of
                                            matcha beverages and treats, crafted
                                            with the finest ingredients.
                                        </h2>
                                    </div>
                                    <button
                                        class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 @[480px]:h-12 @[480px]:px-5 bg-[#4bb814] text-[#121b0e] text-sm font-bold leading-normal tracking-[0.015em] @[480px]:text-base @[480px]:font-bold @[480px]:leading-normal @[480px]:tracking-[0.015em]"
                                    >
                                        <span class="truncate"
                                            >Order Online</span
                                        >
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col gap-10 px-4 py-10 @container">
                            <div class="flex flex-col gap-4">
                                <h1
                                    class="text-[#121b0e] tracking-light text-[32px] font-bold leading-tight @[480px]:text-4xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]"
                                >
                                    Our Signature Creations
                                </h1>
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal max-w-[720px]"
                                >
                                    Indulge in our unique matcha-infused
                                    delights, from creamy lattes to refreshing
                                    desserts.
                                </p>
                            </div>
                            <div
                                class="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3"
                            >
                                <div class="flex flex-col gap-3 pb-3">
                                    <div
                                        class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl"
                                        style="
                                            background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuC-Tv4tgLBuA2FrtbqJk867_jZG9iCjUXKeF1Mgr25E1mzQ1h6GYXvQSMGfg63zdxu3tarZtF7umBZozpIvQnvQPvjqc9gS3xk4UcrcW9M9ALFhR64iXMbohrt9skH5HUnXOGRuWgfooDYzkb_jV7rqlWTJ9W0WYyquJ4ror5_uNPd7EL-IoH165i8nk3otCSOiATIw9w5ZHCpYAjGTMOrSMGsQ_Qkl-4NUvv0mWzxpz2EC9_WDWzo3Mpudi9IeNAmEK9XWTHpxfDM&quot;);
                                        "
                                    ></div>
                                    <div>
                                        <p
                                            class="text-[#121b0e] text-base font-medium leading-normal"
                                        >
                                            Matcha Latte
                                        </p>
                                        <p
                                            class="text-[#67974e] text-sm font-normal leading-normal"
                                        >
                                            A classic blend of premium matcha
                                            and steamed milk, topped with
                                            delicate latte art.
                                        </p>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-3 pb-3">
                                    <div
                                        class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl"
                                        style="
                                            background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuA3X24efnaPCRoo7sv-A08a-70A64WuOWP6TH8W2vdoAYYh24iRzXUKpW8hrWLWr2o0XEBW6AbvVwMAeQpoOV0vtxTcw051lpFjxGfPPkWEpTC37a3vdt7hZyoEUdHV47p1DhClM04KcLt4gF9W94TQ7zF3DzFzr41XaHXzW-0hg1IkEWUfGnNwIJ-XqD2NFQOac4Y_yHqMZ9yOVBeM4k4OVaqCFJ23LrxHbtCogL1axHSos3RbI9Coh_dzMNv-Toi6pTG_s5b6-sw&quot;);
                                        "
                                    ></div>
                                    <div>
                                        <p
                                            class="text-[#121b0e] text-base font-medium leading-normal"
                                        >
                                            Matcha Ice Cream
                                        </p>
                                        <p
                                            class="text-[#67974e] text-sm font-normal leading-normal"
                                        >
                                            A cool and creamy treat with the
                                            distinctive flavor of matcha.
                                        </p>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-3 pb-3">
                                    <div
                                        class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl"
                                        style="
                                            background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuAl4jRYFGealbnMwALDAsC1QtNZaVSvq2B7YKB5xYQQDQCVg6c-EyD9G34Tc2a7sV7tZqyn7X9AqXcbp1vE1YE3ZzkdH2IKZDrZumA0NKwxVKOxN0zd6p8O3P3a4hSn990ezDYbZ2r07LdFgW0p_9XfpYHMrlIpvqX_mO3pnnhuvHeACnr_p2SKBDxtWyRNND7ZHBTWmFDD6ldllrWzu_0J5HQpjzzGFGcIjKS2gFlBsUCtsGC2OS-5JKLvh_PBh1V0SaRhobR2nLQ&quot;);
                                        "
                                    ></div>
                                    <div>
                                        <p
                                            class="text-[#121b0e] text-base font-medium leading-normal"
                                        >
                                            Matcha Cake
                                        </p>
                                        <p
                                            class="text-[#67974e] text-sm font-normal leading-normal"
                                        >
                                            A light and airy cake with a subtle
                                            matcha sweetness.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col gap-10 px-4 py-10 @container">
                            <div class="flex flex-col gap-4">
                                <h1
                                    class="text-[#121b0e] tracking-light text-[32px] font-bold leading-tight @[480px]:text-4xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]"
                                >
                                    Order Online for Delivery or Pickup
                                </h1>
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal max-w-[720px]"
                                >
                                    Enjoy our matcha creations from the comfort
                                    of your home or grab them on the go.
                                </p>
                            </div>
                            <div
                                class="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-0"
                            >
                                <div
                                    class="flex flex-1 gap-3 rounded-lg border border-[#d7e7d0] bg-[#f9fcf8] p-4 flex-col"
                                >
                                    <div
                                        class="text-[#121b0e]"
                                        data-icon="Truck"
                                        data-size="24px"
                                        data-weight="regular"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24px"
                                            height="24px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M247.42,117l-14-35A15.93,15.93,0,0,0,218.58,72H184V64a8,8,0,0,0-8-8H24A16,16,0,0,0,8,72V184a16,16,0,0,0,16,16H41a32,32,0,0,0,62,0h50a32,32,0,0,0,62,0h17a16,16,0,0,0,16-16V120A7.94,7.94,0,0,0,247.42,117ZM184,88h34.58l9.6,24H184ZM24,72H168v64H24ZM72,208a16,16,0,1,1,16-16A16,16,0,0,1,72,208Zm81-24H103a32,32,0,0,0-62,0H24V152H168v12.31A32.11,32.11,0,0,0,153,184Zm31,24a16,16,0,1,1,16-16A16,16,0,0,1,184,208Zm48-24H215a32.06,32.06,0,0,0-31-24V128h48Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div class="flex flex-col gap-1">
                                        <h2
                                            class="text-[#121b0e] text-base font-bold leading-tight"
                                        >
                                            Fast Delivery
                                        </h2>
                                        <p
                                            class="text-[#67974e] text-sm font-normal leading-normal"
                                        >
                                            Get your matcha fix delivered
                                            quickly to your doorstep.
                                        </p>
                                    </div>
                                </div>
                                <div
                                    class="flex flex-1 gap-3 rounded-lg border border-[#d7e7d0] bg-[#f9fcf8] p-4 flex-col"
                                >
                                    <div
                                        class="text-[#121b0e]"
                                        data-icon="Clock"
                                        data-size="24px"
                                        data-weight="regular"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24px"
                                            height="24px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm64-88a8,8,0,0,1-8,8H128a8,8,0,0,1-8-8V72a8,8,0,0,1,16,0v48h48A8,8,0,0,1,192,128Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div class="flex flex-col gap-1">
                                        <h2
                                            class="text-[#121b0e] text-base font-bold leading-tight"
                                        >
                                            Easy Ordering
                                        </h2>
                                        <p
                                            class="text-[#67974e] text-sm font-normal leading-normal"
                                        >
                                            Our online ordering system is simple
                                            and convenient.
                                        </p>
                                    </div>
                                </div>
                                <div
                                    class="flex flex-1 gap-3 rounded-lg border border-[#d7e7d0] bg-[#f9fcf8] p-4 flex-col"
                                >
                                    <div
                                        class="text-[#121b0e]"
                                        data-icon="Star"
                                        data-size="24px"
                                        data-weight="regular"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24px"
                                            height="24px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M239.2,97.29a16,16,0,0,0-13.81-11L166,81.17,142.72,25.81h0a15.95,15.95,0,0,0-29.44,0L90.07,81.17,30.61,86.32a16,16,0,0,0-9.11,28.06L66.61,153.8,53.09,212.34a16,16,0,0,0,23.84,17.34l51-31,51.11,31a16,16,0,0,0,23.84-17.34l-13.51-58.6,45.1-39.36A16,16,0,0,0,239.2,97.29Zm-15.22,5-45.1,39.36a16,16,0,0,0-5.08,15.71L187.35,216v0l-51.07-31a15.9,15.9,0,0,0-16.54,0l-51,31h0L82.2,157.4a16,16,0,0,0-5.08-15.71L32,102.35a.37.37,0,0,1,0-.09l59.44-5.14a16,16,0,0,0,13.35-9.75L128,32.08l23.2,55.29a16,16,0,0,0,13.35,9.75L224,102.26S224,102.32,224,102.33Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div class="flex flex-col gap-1">
                                        <h2
                                            class="text-[#121b0e] text-base font-bold leading-tight"
                                        >
                                            Top Quality
                                        </h2>
                                        <p
                                            class="text-[#67974e] text-sm font-normal leading-normal"
                                        >
                                            We use only the highest quality
                                            matcha in all our products.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h2
                            class="text-[#121b0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5"
                        >
                            Customer Testimonials
                        </h2>
                        <div
                            class="flex flex-col gap-8 overflow-x-hidden bg-[#f9fcf8] p-4"
                        >
                            <div class="flex flex-col gap-3 bg-[#f9fcf8]">
                                <div class="flex items-center gap-3">
                                    <div
                                        class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                                        style="
                                            background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuD9fB8vEWRwzbQR9zdVa5ukSOd5JoMbg1rcSj9-WY0bpIlcD_t5jFBSaTfr46mEdTzUkaf0D1-Z9pGSs92t52cC7K69fiaLaEAXxjqM_kLvIEObHZSkff2cbBsr8aBtxM_9rw9aeww4ItyPtGUf_LoFzsK5FaxbsoMNr7oquijGzpqFgeSq4Fj2dErSgTdmjLfFQIpO8418NHXfp4kO2ftruB4Vegl8rSE7s8O_gpWZ9D6KxibJlyVSJlC9KO-XkXC4n9zzqfc_uqQ&quot;);
                                        "
                                    ></div>
                                    <div class="flex-1">
                                        <p
                                            class="text-[#121b0e] text-base font-medium leading-normal"
                                        >
                                            Clara Bennett
                                        </p>
                                        <p
                                            class="text-[#67974e] text-sm font-normal leading-normal"
                                        >
                                            2 weeks ago
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-0.5">
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                </div>
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    The matcha latte was absolutely divine! The
                                    perfect balance of sweetness and matcha
                                    flavor. Will definitely be ordering again!
                                </p>
                                <div class="flex gap-9 text-[#67974e]">
                                    <button class="flex items-center gap-2">
                                        <div
                                            class="text-inherit"
                                            data-icon="ThumbsUp"
                                            data-size="20px"
                                            data-weight="regular"
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="20px"
                                                height="20px"
                                                fill="currentColor"
                                                viewBox="0 0 256 256"
                                            >
                                                <path
                                                    d="M234,80.12A24,24,0,0,0,216,72H160V56a40,40,0,0,0-40-40,8,8,0,0,0-7.16,4.42L75.06,96H32a16,16,0,0,0-16,16v88a16,16,0,0,0,16,16H204a24,24,0,0,0,23.82-21l12-96A24,24,0,0,0,234,80.12ZM32,112H72v88H32ZM223.94,97l-12,96a8,8,0,0,1-7.94,7H88V105.89l36.71-73.43A24,24,0,0,1,144,56V80a8,8,0,0,0,8,8h64a8,8,0,0,1,7.94,9Z"
                                                ></path>
                                            </svg>
                                        </div>
                                        <p class="text-inherit">5</p>
                                    </button>
                                    <button class="flex items-center gap-2">
                                        <div
                                            class="text-inherit"
                                            data-icon="ThumbsDown"
                                            data-size="20px"
                                            data-weight="regular"
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="20px"
                                                height="20px"
                                                fill="currentColor"
                                                viewBox="0 0 256 256"
                                            >
                                                <path
                                                    d="M239.82,157l-12-96A24,24,0,0,0,204,40H32A16,16,0,0,0,16,56v88a16,16,0,0,0,16,16H75.06l37.78,75.58A8,8,0,0,0,120,240a40,40,0,0,0,40-40V184h56a24,24,0,0,0,23.82-27ZM72,144H32V56H72Zm150,21.29a7.88,7.88,0,0,1-6,2.71H152a8,8,0,0,0-8,8v24a24,24,0,0,1-19.29,23.54L88,150.11V56H204a8,8,0,0,1,7.94,7l12,96A7.87,7.87,0,0,1,222,165.29Z"
                                                ></path>
                                            </svg>
                                        </div>
                                        <p class="text-inherit">1</p>
                                    </button>
                                </div>
                            </div>
                            <div class="flex flex-col gap-3 bg-[#f9fcf8]">
                                <div class="flex items-center gap-3">
                                    <div
                                        class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                                        style="
                                            background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuAmsvTZ6VLsFVbUW8dIbHrqW6YTPhhFxn9oXE2U6EPZwms3_kcu0bRNWD50dhc4R_U-n4C-VvmNbY_WDs6fiMuctZG8TMZWiU10GejO02xpKnoeT7lVCXF4C5KI6DueFvDMRh7QTZjnuQvD9eQNBguu4sXn5bZUYyoAFAcdSriKDWszG9KCg1OD1AudXpvSkFOHVDB6cvstTCqEHLtUpxB4SwWQjNmLlyQw-_vpqyw32pi_6NhTXJOVQiq0ru071vau1hqUQ1R0Vm8&quot;);
                                        "
                                    ></div>
                                    <div class="flex-1">
                                        <p
                                            class="text-[#121b0e] text-base font-medium leading-normal"
                                        >
                                            Owen Carter
                                        </p>
                                        <p
                                            class="text-[#67974e] text-sm font-normal leading-normal"
                                        >
                                            1 month ago
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-0.5">
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div
                                        class="text-[#bbd5ae]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="regular"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M239.2,97.29a16,16,0,0,0-13.81-11L166,81.17,142.72,25.81h0a15.95,15.95,0,0,0-29.44,0L90.07,81.17,30.61,86.32a16,16,0,0,0-9.11,28.06L66.61,153.8,53.09,212.34a16,16,0,0,0,23.84,17.34l51-31,51.11,31a16,16,0,0,0,23.84-17.34l-13.51-58.6,45.1-39.36A16,16,0,0,0,239.2,97.29Zm-15.22,5-45.1,39.36a16,16,0,0,0-5.08,15.71L187.35,216v0l-51.07-31a15.9,15.9,0,0,0-16.54,0l-51,31h0L82.2,157.4a16,16,0,0,0-5.08-15.71L32,102.35a.37.37,0,0,1,0-.09l59.44-5.14a16,16,0,0,0,13.35-9.75L128,32.08l23.2,55.29a16,16,0,0,0,13.35,9.75L224,102.26S224,102.32,224,102.33Z"
                                            ></path>
                                        </svg>
                                    </div>
                                </div>
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    I tried the matcha ice cream and it was a
                                    refreshing treat. The matcha taste was
                                    subtle but delicious.
                                </p>
                                <div class="flex gap-9 text-[#67974e]">
                                    <button class="flex items-center gap-2">
                                        <div
                                            class="text-inherit"
                                            data-icon="ThumbsUp"
                                            data-size="20px"
                                            data-weight="regular"
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="20px"
                                                height="20px"
                                                fill="currentColor"
                                                viewBox="0 0 256 256"
                                            >
                                                <path
                                                    d="M234,80.12A24,24,0,0,0,216,72H160V56a40,40,0,0,0-40-40,8,8,0,0,0-7.16,4.42L75.06,96H32a16,16,0,0,0-16,16v88a16,16,0,0,0,16,16H204a24,24,0,0,0,23.82-21l12-96A24,24,0,0,0,234,80.12ZM32,112H72v88H32ZM223.94,97l-12,96a8,8,0,0,1-7.94,7H88V105.89l36.71-73.43A24,24,0,0,1,144,56V80a8,8,0,0,0,8,8h64a8,8,0,0,1,7.94,9Z"
                                                ></path>
                                            </svg>
                                        </div>
                                        <p class="text-inherit">3</p>
                                    </button>
                                    <button class="flex items-center gap-2">
                                        <div
                                            class="text-inherit"
                                            data-icon="ThumbsDown"
                                            data-size="20px"
                                            data-weight="regular"
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="20px"
                                                height="20px"
                                                fill="currentColor"
                                                viewBox="0 0 256 256"
                                            >
                                                <path
                                                    d="M239.82,157l-12-96A24,24,0,0,0,204,40H32A16,16,0,0,0,16,56v88a16,16,0,0,0,16,16H75.06l37.78,75.58A8,8,0,0,0,120,240a40,40,0,0,0,40-40V184h56a24,24,0,0,0,23.82-27ZM72,144H32V56H72Zm150,21.29a7.88,7.88,0,0,1-6,2.71H152a8,8,0,0,0-8,8v24a24,24,0,0,1-19.29,23.54L88,150.11V56H204a8,8,0,0,1,7.94,7l12,96A7.87,7.87,0,0,1,222,165.29Z"
                                                ></path>
                                            </svg>
                                        </div>
                                    </button>
                                </div>
                            </div>
                            <div class="flex flex-col gap-3 bg-[#f9fcf8]">
                                <div class="flex items-center gap-3">
                                    <div
                                        class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                                        style="
                                            background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuD3qp5n0ZwSZROADKqVJzsa3y7JSuulEKxda-sXaZvFVBTL7peN_MiR7ufzoR1tQGyEpvRT0NFerA512kTanpVXDd5hR2V32YzQWT-fjL-FRWCPVJ5WSKP1A89C35rt2CJonKoJViW2O92EqmcT9hMEpIe9AheXrX5-A2W8rmUaXTCvVWhy-5ob9dzCLOcxGlFeYGutONPenkEWVWxUFLdgXHrBvbbnuLaeBfSP_cOLQtic7Ki22fQcCM_b0ZxaZH7cVqsACAw-g-Y&quot;);
                                        "
                                    ></div>
                                    <div class="flex-1">
                                        <p
                                            class="text-[#121b0e] text-base font-medium leading-normal"
                                        >
                                            Emma Davis
                                        </p>
                                        <p
                                            class="text-[#67974e] text-sm font-normal leading-normal"
                                        >
                                            2 months ago
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-0.5">
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div
                                        class="text-[#4bb814]"
                                        data-icon="Star"
                                        data-size="20px"
                                        data-weight="fill"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M234.5,114.38l-45.1,39.36,13.51,58.6a16,16,0,0,1-23.84,17.34l-51.11-31-51,31a16,16,0,0,1-23.84-17.34L66.61,153.8,21.5,114.38a16,16,0,0,1,9.11-28.06l59.46-5.15,23.21-55.36a15.95,15.95,0,0,1,29.44,0h0L166,81.17l59.44,5.15a16,16,0,0,1,9.11,28.06Z"
                                            ></path>
                                        </svg>
                                    </div>
                                </div>
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    The matcha cake was a delightful surprise!
                                    Light, fluffy, and not too sweet. A perfect
                                    dessert to end my day.
                                </p>
                                <div class="flex gap-9 text-[#67974e]">
                                    <button class="flex items-center gap-2">
                                        <div
                                            class="text-inherit"
                                            data-icon="ThumbsUp"
                                            data-size="20px"
                                            data-weight="regular"
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="20px"
                                                height="20px"
                                                fill="currentColor"
                                                viewBox="0 0 256 256"
                                            >
                                                <path
                                                    d="M234,80.12A24,24,0,0,0,216,72H160V56a40,40,0,0,0-40-40,8,8,0,0,0-7.16,4.42L75.06,96H32a16,16,0,0,0-16,16v88a16,16,0,0,0,16,16H204a24,24,0,0,0,23.82-21l12-96A24,24,0,0,0,234,80.12ZM32,112H72v88H32ZM223.94,97l-12,96a8,8,0,0,1-7.94,7H88V105.89l36.71-73.43A24,24,0,0,1,144,56V80a8,8,0,0,0,8,8h64a8,8,0,0,1,7.94,9Z"
                                                ></path>
                                            </svg>
                                        </div>
                                        <p class="text-inherit">7</p>
                                    </button>
                                    <button class="flex items-center gap-2">
                                        <div
                                            class="text-inherit"
                                            data-icon="ThumbsDown"
                                            data-size="20px"
                                            data-weight="regular"
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="20px"
                                                height="20px"
                                                fill="currentColor"
                                                viewBox="0 0 256 256"
                                            >
                                                <path
                                                    d="M239.82,157l-12-96A24,24,0,0,0,204,40H32A16,16,0,0,0,16,56v88a16,16,0,0,0,16,16H75.06l37.78,75.58A8,8,0,0,0,120,240a40,40,0,0,0,40-40V184h56a24,24,0,0,0,23.82-27ZM72,144H32V56H72Zm150,21.29a7.88,7.88,0,0,1-6,2.71H152a8,8,0,0,0-8,8v24a24,24,0,0,1-19.29,23.54L88,150.11V56H204a8,8,0,0,1,7.94,7l12,96A7.87,7.87,0,0,1,222,165.29Z"
                                                ></path>
                                            </svg>
                                        </div>
                                        <p class="text-inherit">2</p>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <footer class="flex justify-center">
                    <div class="flex max-w-[960px] flex-1 flex-col">
                        <footer
                            class="flex flex-col gap-6 px-5 py-10 text-center @container"
                        >
                            <div
                                class="flex flex-wrap items-center justify-center gap-6 @[480px]:flex-row @[480px]:justify-around"
                            >
                                <a
                                    class="text-[#67974e] text-base font-normal leading-normal min-w-40"
                                    href="#"
                                    >Privacy Policy</a
                                >
                                <a
                                    class="text-[#67974e] text-base font-normal leading-normal min-w-40"
                                    href="#"
                                    >Terms of Service</a
                                >
                                <a
                                    class="text-[#67974e] text-base font-normal leading-normal min-w-40"
                                    href="#"
                                    >Contact Us</a
                                >
                            </div>
                            <div class="flex flex-wrap justify-center gap-4">
                                <a href="#">
                                    <div
                                        class="text-[#67974e]"
                                        data-icon="InstagramLogo"
                                        data-size="24px"
                                        data-weight="regular"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24px"
                                            height="24px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160ZM176,24H80A56.06,56.06,0,0,0,24,80v96a56.06,56.06,0,0,0,56,56h96a56.06,56.06,0,0,0,56-56V80A56.06,56.06,0,0,0,176,24Zm40,152a40,40,0,0,1-40,40H80a40,40,0,0,1-40-40V80A40,40,0,0,1,80,40h96a40,40,0,0,1,40,40ZM192,76a12,12,0,1,1-12-12A12,12,0,0,1,192,76Z"
                                            ></path>
                                        </svg>
                                    </div>
                                </a>
                                <a href="#">
                                    <div
                                        class="text-[#67974e]"
                                        data-icon="TwitterLogo"
                                        data-size="24px"
                                        data-weight="regular"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24px"
                                            height="24px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M247.39,68.94A8,8,0,0,0,240,64H209.57A48.66,48.66,0,0,0,168.1,40a46.91,46.91,0,0,0-33.75,13.7A47.9,47.9,0,0,0,120,88v6.09C79.74,83.47,46.81,50.72,46.46,50.37a8,8,0,0,0-13.65,4.92c-4.31,47.79,9.57,79.77,22,98.18a110.93,110.93,0,0,0,21.88,24.2c-15.23,17.53-39.21,26.74-39.47,26.84a8,8,0,0,0-3.85,11.93c.75,1.12,3.75,5.05,11.08,8.72C53.51,229.7,65.48,232,80,232c70.67,0,129.72-54.42,135.75-124.44l29.91-29.9A8,8,0,0,0,247.39,68.94Zm-45,29.41a8,8,0,0,0-2.32,5.14C196,166.58,143.28,216,80,216c-10.56,0-18-1.4-23.22-3.08,11.51-6.25,27.56-17,37.88-32.48A8,8,0,0,0,92,169.08c-.47-.27-43.91-26.34-44-96,16,13,45.25,33.17,78.67,38.79A8,8,0,0,0,136,104V88a32,32,0,0,1,9.6-22.92A30.94,30.94,0,0,1,167.9,56c12.66.16,24.49,7.88,29.44,19.21A8,8,0,0,0,204.67,80h16Z"
                                            ></path>
                                        </svg>
                                    </div>
                                </a>
                                <a href="#">
                                    <div
                                        class="text-[#67974e]"
                                        data-icon="FacebookLogo"
                                        data-size="24px"
                                        data-weight="regular"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24px"
                                            height="24px"
                                            fill="currentColor"
                                            viewBox="0 0 256 256"
                                        >
                                            <path
                                                d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm8,191.63V152h24a8,8,0,0,0,0-16H136V112a16,16,0,0,1,16-16h16a8,8,0,0,0,0-16H152a32,32,0,0,0-32,32v24H96a8,8,0,0,0,0,16h24v63.63a88,88,0,1,1,16,0Z"
                                            ></path>
                                        </svg>
                                    </div>
                                </a>
                            </div>
                            <p
                                class="text-[#67974e] text-base font-normal leading-normal"
                            >
                                @2024 Matcha Cafe. All rights reserved.
                            </p>
                        </footer>
                    </div>
                </footer>
            </div>
        </div>
    </body>
</html>

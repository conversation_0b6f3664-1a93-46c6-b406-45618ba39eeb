<html>
    <head>
        <link
            rel="preconnect"
            href="https://fonts.gstatic.com/"
            crossorigin=""
        />
        <link
            rel="stylesheet"
            as="style"
            onload="this.rel='stylesheet'"
            href="https://fonts.googleapis.com/css2?display=swap&amp;family=Epilogue%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
        />

        <title>Stitch Design</title>
        <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

        <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    </head>
    <body>
        <div
            class="relative flex size-full min-h-screen flex-col bg-[#f9fcf8] group/design-root overflow-x-hidden"
            style="
                --checkbox-tick-svg: url(&quot;data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27rgb(18,27,14)%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e&quot;);
                font-family: Epilogue, &quot;Noto Sans&quot;, sans-serif;
            "
        >
            <div class="layout-container flex h-full grow flex-col">
                <header
                    class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#ebf3e7] px-10 py-3"
                >
                    <div class="flex items-center gap-4 text-[#121b0e]">
                        <div class="size-4">
                            <svg
                                viewBox="0 0 48 48"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <g clip-path="url(#clip0_6_535)">
                                    <path
                                        fill-rule="evenodd"
                                        clip-rule="evenodd"
                                        d="M47.2426 24L24 47.2426L0.757355 24L24 0.757355L47.2426 24ZM12.2426 21H35.7574L24 9.24264L12.2426 21Z"
                                        fill="currentColor"
                                    ></path>
                                </g>
                                <defs>
                                    <clipPath id="clip0_6_535">
                                        <rect
                                            width="48"
                                            height="48"
                                            fill="white"
                                        ></rect>
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <h2
                            class="text-[#121b0e] text-lg font-bold leading-tight tracking-[-0.015em]"
                        >
                            Matcha Cafe
                        </h2>
                    </div>
                    <div class="flex flex-1 justify-end gap-8">
                        <div class="flex items-center gap-9">
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Menu</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Catering</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Gift Cards</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Rewards</a
                            >
                        </div>
                        <div class="flex gap-2">
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#ebf3e7] text-[#121b0e] text-sm font-bold leading-normal tracking-[0.015em]"
                            >
                                <span class="truncate">Order Online</span>
                            </button>
                            <button
                                class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 bg-[#ebf3e7] text-[#121b0e] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
                            >
                                <div
                                    class="text-[#121b0e]"
                                    data-icon="MagnifyingGlass"
                                    data-size="20px"
                                    data-weight="regular"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="20px"
                                        height="20px"
                                        fill="currentColor"
                                        viewBox="0 0 256 256"
                                    >
                                        <path
                                            d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                                        ></path>
                                    </svg>
                                </div>
                            </button>
                            <button
                                class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 bg-[#ebf3e7] text-[#121b0e] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
                            >
                                <div
                                    class="text-[#121b0e]"
                                    data-icon="ShoppingCart"
                                    data-size="20px"
                                    data-weight="regular"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="20px"
                                        height="20px"
                                        fill="currentColor"
                                        viewBox="0 0 256 256"
                                    >
                                        <path
                                            d="M222.14,58.87A8,8,0,0,0,216,56H54.68L49.79,29.14A16,16,0,0,0,34.05,16H16a8,8,0,0,0,0,16h18L59.56,172.29a24,24,0,0,0,5.33,11.27,28,28,0,1,0,44.4,8.44h45.42A27.75,27.75,0,0,0,152,204a28,28,0,1,0,28-28H83.17a8,8,0,0,1-7.87-6.57L72.13,152h116a24,24,0,0,0,23.61-19.71l12.16-66.86A8,8,0,0,0,222.14,58.87ZM96,204a12,12,0,1,1-12-12A12,12,0,0,1,96,204Zm96,0a12,12,0,1,1-12-12A12,12,0,0,1,192,204Zm4-74.57A8,8,0,0,1,188.1,136H69.22L57.59,72H206.41Z"
                                        ></path>
                                    </svg>
                                </div>
                            </button>
                        </div>
                    </div>
                </header>
                <div class="px-40 flex flex-1 justify-center py-5">
                    <div
                        class="layout-content-container flex flex-col w-[512px] max-w-[512px] py-5 max-w-[960px] flex-1"
                    >
                        <div class="flex flex-wrap gap-2 p-4">
                            <a
                                class="text-[#67974e] text-base font-medium leading-normal"
                                href="#"
                                >Menu</a
                            >
                            <span
                                class="text-[#67974e] text-base font-medium leading-normal"
                                >/</span
                            >
                            <a
                                class="text-[#67974e] text-base font-medium leading-normal"
                                href="#"
                                >Drinks</a
                            >
                            <span
                                class="text-[#67974e] text-base font-medium leading-normal"
                                >/</span
                            >
                            <span
                                class="text-[#121b0e] text-base font-medium leading-normal"
                                >Matcha Latte</span
                            >
                        </div>
                        <h1
                            class="text-[#121b0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 text-left pb-3 pt-5"
                        >
                            Matcha Latte
                        </h1>
                        <p
                            class="text-[#121b0e] text-base font-normal leading-normal pb-3 pt-1 px-4"
                        >
                            Our signature matcha latte, made with premium matcha
                            powder and your choice of milk. Customize your
                            sweetness and add-ons for a perfect cup.
                        </p>
                        <div
                            class="flex w-full grow bg-[#f9fcf8] @container p-4"
                        >
                            <div
                                class="w-full gap-1 overflow-hidden bg-[#f9fcf8] @[480px]:gap-2 aspect-[3/2] rounded-xl flex"
                            >
                                <div
                                    class="w-full bg-center bg-no-repeat bg-cover aspect-auto rounded-none flex-1"
                                    style="
                                        background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuAPQqZTFSiESR9SHS7vZK2JgJIIW4DLOh3XFhU3U4Q0gXjkTS6FsNEEFK-1h0RsK4c_fyA7HAEVAZOv20wQgUBJr48k4a1elX3INyTtuKnMo9xvn5laiNnUaveJ-UNVPkKJVnti8jrpNw2DmBPBVFszr6Sl-BAGARaImQo9ERWxDcaTBsCSJUthNDDMuyXq07zGFMjaygweJLBwwJZtHz4J2QUfAzR2me4c57ACxUlgqv3oC4FDdxwWiJ0sM51KpFhoUY40Eg5eG4s&quot;);
                                    "
                                ></div>
                            </div>
                        </div>
                        <h2
                            class="text-[#121b0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5"
                        >
                            Size
                        </h2>
                        <div class="flex flex-wrap gap-3 p-4">
                            <label
                                class="text-sm font-medium leading-normal flex items-center justify-center rounded-xl border border-[#d7e7d0] px-4 h-11 text-[#121b0e] has-[:checked]:border-[3px] has-[:checked]:px-3.5 has-[:checked]:border-[#4bb814] relative cursor-pointer"
                            >
                                Small
                                <input
                                    type="radio"
                                    class="invisible absolute"
                                    name="0a2b2579-cf59-4fe4-ad78-cba9eaedc323"
                                />
                            </label>
                            <label
                                class="text-sm font-medium leading-normal flex items-center justify-center rounded-xl border border-[#d7e7d0] px-4 h-11 text-[#121b0e] has-[:checked]:border-[3px] has-[:checked]:px-3.5 has-[:checked]:border-[#4bb814] relative cursor-pointer"
                            >
                                Medium
                                <input
                                    type="radio"
                                    class="invisible absolute"
                                    name="0a2b2579-cf59-4fe4-ad78-cba9eaedc323"
                                />
                            </label>
                            <label
                                class="text-sm font-medium leading-normal flex items-center justify-center rounded-xl border border-[#d7e7d0] px-4 h-11 text-[#121b0e] has-[:checked]:border-[3px] has-[:checked]:px-3.5 has-[:checked]:border-[#4bb814] relative cursor-pointer"
                            >
                                Large
                                <input
                                    type="radio"
                                    class="invisible absolute"
                                    name="0a2b2579-cf59-4fe4-ad78-cba9eaedc323"
                                />
                            </label>
                        </div>
                        <h2
                            class="text-[#121b0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5"
                        >
                            Milk
                        </h2>
                        <div class="flex flex-wrap gap-3 p-4">
                            <label
                                class="text-sm font-medium leading-normal flex items-center justify-center rounded-xl border border-[#d7e7d0] px-4 h-11 text-[#121b0e] has-[:checked]:border-[3px] has-[:checked]:px-3.5 has-[:checked]:border-[#4bb814] relative cursor-pointer"
                            >
                                Whole Milk
                                <input
                                    type="radio"
                                    class="invisible absolute"
                                    name="35fe0268-b4a6-4a89-8322-8a433f59fc63"
                                />
                            </label>
                            <label
                                class="text-sm font-medium leading-normal flex items-center justify-center rounded-xl border border-[#d7e7d0] px-4 h-11 text-[#121b0e] has-[:checked]:border-[3px] has-[:checked]:px-3.5 has-[:checked]:border-[#4bb814] relative cursor-pointer"
                            >
                                Almond Milk
                                <input
                                    type="radio"
                                    class="invisible absolute"
                                    name="35fe0268-b4a6-4a89-8322-8a433f59fc63"
                                />
                            </label>
                            <label
                                class="text-sm font-medium leading-normal flex items-center justify-center rounded-xl border border-[#d7e7d0] px-4 h-11 text-[#121b0e] has-[:checked]:border-[3px] has-[:checked]:px-3.5 has-[:checked]:border-[#4bb814] relative cursor-pointer"
                            >
                                Oat Milk
                                <input
                                    type="radio"
                                    class="invisible absolute"
                                    name="35fe0268-b4a6-4a89-8322-8a433f59fc63"
                                />
                            </label>
                            <label
                                class="text-sm font-medium leading-normal flex items-center justify-center rounded-xl border border-[#d7e7d0] px-4 h-11 text-[#121b0e] has-[:checked]:border-[3px] has-[:checked]:px-3.5 has-[:checked]:border-[#4bb814] relative cursor-pointer"
                            >
                                Soy Milk
                                <input
                                    type="radio"
                                    class="invisible absolute"
                                    name="35fe0268-b4a6-4a89-8322-8a433f59fc63"
                                />
                            </label>
                        </div>
                        <h2
                            class="text-[#121b0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5"
                        >
                            Sweetness
                        </h2>
                        <div class="flex flex-wrap gap-3 p-4">
                            <label
                                class="text-sm font-medium leading-normal flex items-center justify-center rounded-xl border border-[#d7e7d0] px-4 h-11 text-[#121b0e] has-[:checked]:border-[3px] has-[:checked]:px-3.5 has-[:checked]:border-[#4bb814] relative cursor-pointer"
                            >
                                Unsweetened
                                <input
                                    type="radio"
                                    class="invisible absolute"
                                    name="6fcdfd38-2008-4fa4-ba79-821e3dec6069"
                                />
                            </label>
                            <label
                                class="text-sm font-medium leading-normal flex items-center justify-center rounded-xl border border-[#d7e7d0] px-4 h-11 text-[#121b0e] has-[:checked]:border-[3px] has-[:checked]:px-3.5 has-[:checked]:border-[#4bb814] relative cursor-pointer"
                            >
                                Half Sweet
                                <input
                                    type="radio"
                                    class="invisible absolute"
                                    name="6fcdfd38-2008-4fa4-ba79-821e3dec6069"
                                />
                            </label>
                            <label
                                class="text-sm font-medium leading-normal flex items-center justify-center rounded-xl border border-[#d7e7d0] px-4 h-11 text-[#121b0e] has-[:checked]:border-[3px] has-[:checked]:px-3.5 has-[:checked]:border-[#4bb814] relative cursor-pointer"
                            >
                                Regular Sweet
                                <input
                                    type="radio"
                                    class="invisible absolute"
                                    name="6fcdfd38-2008-4fa4-ba79-821e3dec6069"
                                />
                            </label>
                            <label
                                class="text-sm font-medium leading-normal flex items-center justify-center rounded-xl border border-[#d7e7d0] px-4 h-11 text-[#121b0e] has-[:checked]:border-[3px] has-[:checked]:px-3.5 has-[:checked]:border-[#4bb814] relative cursor-pointer"
                            >
                                Extra Sweet
                                <input
                                    type="radio"
                                    class="invisible absolute"
                                    name="6fcdfd38-2008-4fa4-ba79-821e3dec6069"
                                />
                            </label>
                        </div>
                        <h2
                            class="text-[#121b0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5"
                        >
                            Add-ons
                        </h2>
                        <div class="px-4">
                            <label class="flex gap-x-3 py-3 flex-row">
                                <input
                                    type="checkbox"
                                    class="h-5 w-5 rounded border-[#d7e7d0] border-2 bg-transparent text-[#4bb814] checked:bg-[#4bb814] checked:border-[#4bb814] checked:bg-[image:--checkbox-tick-svg] focus:ring-0 focus:ring-offset-0 focus:border-[#d7e7d0] focus:outline-none"
                                />
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    Whipped Cream
                                </p>
                            </label>
                            <label class="flex gap-x-3 py-3 flex-row">
                                <input
                                    type="checkbox"
                                    class="h-5 w-5 rounded border-[#d7e7d0] border-2 bg-transparent text-[#4bb814] checked:bg-[#4bb814] checked:border-[#4bb814] checked:bg-[image:--checkbox-tick-svg] focus:ring-0 focus:ring-offset-0 focus:border-[#d7e7d0] focus:outline-none"
                                />
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    Caramel Drizzle
                                </p>
                            </label>
                            <label class="flex gap-x-3 py-3 flex-row">
                                <input
                                    type="checkbox"
                                    class="h-5 w-5 rounded border-[#d7e7d0] border-2 bg-transparent text-[#4bb814] checked:bg-[#4bb814] checked:border-[#4bb814] checked:bg-[image:--checkbox-tick-svg] focus:ring-0 focus:ring-offset-0 focus:border-[#d7e7d0] focus:outline-none"
                                />
                                <p
                                    class="text-[#121b0e] text-base font-normal leading-normal"
                                >
                                    Chocolate Shavings
                                </p>
                            </label>
                        </div>
                        <div
                            class="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3"
                        >
                            <label class="flex flex-col min-w-40 flex-1">
                                <textarea
                                    placeholder="Special Instructions"
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#121b0e] focus:outline-0 focus:ring-0 border border-[#d7e7d0] bg-[#f9fcf8] focus:border-[#d7e7d0] min-h-36 placeholder:text-[#67974e] p-[15px] text-base font-normal leading-normal"
                                ></textarea>
                            </label>
                        </div>
                        <div class="flex px-4 py-3">
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 flex-1 bg-[#4bb814] text-[#121b0e] text-sm font-bold leading-normal tracking-[0.015em]"
                            >
                                <span class="truncate">Add to Order</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>

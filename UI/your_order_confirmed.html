<html>
    <head>
        <link
            rel="preconnect"
            href="https://fonts.gstatic.com/"
            crossorigin=""
        />
        <link
            rel="stylesheet"
            as="style"
            onload="this.rel='stylesheet'"
            href="https://fonts.googleapis.com/css2?display=swap&amp;family=Epilogue%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
        />

        <title>Stitch Design</title>
        <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

        <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    </head>
    <body>
        <div
            class="relative flex size-full min-h-screen flex-col bg-[#f9fcf8] group/design-root overflow-x-hidden"
            style="font-family: Epilogue, &quot;Noto Sans&quot;, sans-serif"
        >
            <div class="layout-container flex h-full grow flex-col">
                <header
                    class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#ebf3e7] px-10 py-3"
                >
                    <div class="flex items-center gap-4 text-[#121b0e]">
                        <div class="size-4">
                            <svg
                                viewBox="0 0 48 48"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <g clip-path="url(#clip0_6_535)">
                                    <path
                                        fill-rule="evenodd"
                                        clip-rule="evenodd"
                                        d="M47.2426 24L24 47.2426L0.757355 24L24 0.757355L47.2426 24ZM12.2426 21H35.7574L24 9.24264L12.2426 21Z"
                                        fill="currentColor"
                                    ></path>
                                </g>
                                <defs>
                                    <clipPath id="clip0_6_535">
                                        <rect
                                            width="48"
                                            height="48"
                                            fill="white"
                                        ></rect>
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <h2
                            class="text-[#121b0e] text-lg font-bold leading-tight tracking-[-0.015em]"
                        >
                            Matcha Cafe
                        </h2>
                    </div>
                    <div class="flex flex-1 justify-end gap-8">
                        <div class="flex items-center gap-9">
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Menu</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Rewards</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Gift Cards</a
                            >
                        </div>
                        <div class="flex gap-2">
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#ebf3e7] text-[#121b0e] text-sm font-bold leading-normal tracking-[0.015em]"
                            >
                                <span class="truncate">Find a store</span>
                            </button>
                            <button
                                class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 bg-[#ebf3e7] text-[#121b0e] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
                            >
                                <div
                                    class="text-[#121b0e]"
                                    data-icon="User"
                                    data-size="20px"
                                    data-weight="regular"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="20px"
                                        height="20px"
                                        fill="currentColor"
                                        viewBox="0 0 256 256"
                                    >
                                        <path
                                            d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"
                                        ></path>
                                    </svg>
                                </div>
                            </button>
                        </div>
                        <div
                            class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                            style="
                                background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuDOctVBz33gIcr4TkPOMHlI52cpgk-KwDRrkIpONcI76qLzM-XqX8x_0dEkYH0DzEusGg5xQT8z1JlmY4AeO1vfwEEbyEbHtHJ3lorqtPwLVfxDzR03O_BQZXjEMy5HfQtL1oEqKA9ZzFKgSvKtL2iRqgw7_y7Ia14OXVoEMh8nB_Tm9AqCJVA4yhGndUysecf9Dtebz1RahfsKeBbG8UcrLIGfPajthcy0wY-SXK_npkW14kGKeOscJUfgK4rgqeIvHK7QvbgzOwk&quot;);
                            "
                        ></div>
                    </div>
                </header>
                <div class="px-40 flex flex-1 justify-center py-5">
                    <div
                        class="layout-content-container flex flex-col w-[512px] max-w-[512px] py-5 max-w-[960px] flex-1"
                    >
                        <h2
                            class="text-[#121b0e] tracking-light text-[28px] font-bold leading-tight px-4 text-center pb-3 pt-5"
                        >
                            Your order is confirmed!
                        </h2>
                        <p
                            class="text-[#121b0e] text-base font-normal leading-normal pb-3 pt-1 px-4 text-center"
                        >
                            We've received your order and are preparing it.
                            You'll receive a notification when it's ready for
                            pickup.
                        </p>
                        <div
                            class="flex w-full grow bg-[#f9fcf8] @container p-4"
                        >
                            <div
                                class="w-full gap-1 overflow-hidden bg-[#f9fcf8] @[480px]:gap-2 aspect-[3/2] rounded-xl flex"
                            >
                                <div
                                    class="w-full bg-center bg-no-repeat bg-cover aspect-auto rounded-none flex-1"
                                    style="
                                        background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuCAfuXuJWjP7W1PUJpbHFvDG9o8D4lvOwECtANCOmO-X-gzdejdFlQy2coTv91nSzK4Vk0f0v14ns8nGtFC4XqpnpXYSCCWHj5ZrEbHH13yvbh8MefpscJ8uAv_wyp98F985l7BsYgDPiwy-Re13nGqaZOYsg5-S4uXPUkrFPrzBFhvc9_SfzH9dIF0bGN79HLgMxVvK69nQxn6T98_N3xRuKvnrPGhAOcwNpDGwkXcyS1Rohzjlf5xljVW6dd5ZQy4tz5zc8ajWsY&quot;);
                                    "
                                ></div>
                            </div>
                        </div>
                        <h3
                            class="text-[#121b0e] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4"
                        >
                            Order Details
                        </h3>
                        <div class="p-4 grid grid-cols-[20%_1fr] gap-x-6">
                            <div
                                class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d7e7d0] py-5"
                            >
                                <p
                                    class="text-[#67974e] text-sm font-normal leading-normal"
                                >
                                    Order Number
                                </p>
                                <p
                                    class="text-[#121b0e] text-sm font-normal leading-normal"
                                >
                                    #123456
                                </p>
                            </div>
                            <div
                                class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d7e7d0] py-5"
                            >
                                <p
                                    class="text-[#67974e] text-sm font-normal leading-normal"
                                >
                                    Order Time
                                </p>
                                <p
                                    class="text-[#121b0e] text-sm font-normal leading-normal"
                                >
                                    10:30 AM
                                </p>
                            </div>
                            <div
                                class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d7e7d0] py-5"
                            >
                                <p
                                    class="text-[#67974e] text-sm font-normal leading-normal"
                                >
                                    Estimated Pickup Time
                                </p>
                                <p
                                    class="text-[#121b0e] text-sm font-normal leading-normal"
                                >
                                    11:00 AM
                                </p>
                            </div>
                        </div>
                        <h3
                            class="text-[#121b0e] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4"
                        >
                            Items
                        </h3>
                        <div
                            class="flex items-center gap-4 bg-[#f9fcf8] px-4 min-h-[72px] py-2"
                        >
                            <div
                                class="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-14"
                                style="
                                    background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuCIMkdfwNEuGb0e2uxM3clq7G48OCzPMZQ7ElEXHccbBWgG7NCzEdc9ZQC_OHN3WWBCDyRx644_LC1WZYbi1p4GNYsIquGJztk88TwAkLWWQkWLmZDW6TpC2ooFcP29fO5U4wOvycW-S8G6nwBAq-MdguTVPmQtljrhcf5VBgGEXZnljZJjy4zA9xEdZYbI2A6TvgCNmNXaVMpBhZa1zzM9Qvc0nabDEC5dX87uhMLU-sl65hGw2SOtIFUguWqng1QrFB9w9jDbEyA&quot;);
                                "
                            ></div>
                            <div class="flex flex-col justify-center">
                                <p
                                    class="text-[#121b0e] text-base font-medium leading-normal line-clamp-1"
                                >
                                    Matcha Latte
                                </p>
                                <p
                                    class="text-[#67974e] text-sm font-normal leading-normal line-clamp-2"
                                >
                                    1 item
                                </p>
                            </div>
                        </div>
                        <div
                            class="flex items-center gap-4 bg-[#f9fcf8] px-4 min-h-[72px] py-2"
                        >
                            <div
                                class="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-14"
                                style="
                                    background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuAqWbnV8t2zyImbDSUe4x4UPdn1-LF72uGVj9bxBA6p8JC5IM6TGBFwrUoiyCDk0HwNcNy_WGnXyaOaC0LaId0ZqwNh_u41k6LJUW0uMiRS_yVIZbtDAvrKxtr0VSzIGYw2qbzQaD6jjSakAdB8mpPq6SfoLeKmfxo6wp6Qx5kXwn9BBBZRk_qCuR9jLJ5otXMHzQvpZ-eodh9mVEoVDUSCpttvjdoizkWHYDN_-y5L7sM2DWhzlel6IxrZOkPYh7Z2-z-GwquIy0U&quot;);
                                "
                            ></div>
                            <div class="flex flex-col justify-center">
                                <p
                                    class="text-[#121b0e] text-base font-medium leading-normal line-clamp-1"
                                >
                                    Matcha Ice Cream
                                </p>
                                <p
                                    class="text-[#67974e] text-sm font-normal leading-normal line-clamp-2"
                                >
                                    1 item
                                </p>
                            </div>
                        </div>
                        <h3
                            class="text-[#121b0e] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4"
                        >
                            Payment
                        </h3>
                        <div class="p-4 grid grid-cols-[20%_1fr] gap-x-6">
                            <div
                                class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d7e7d0] py-5"
                            >
                                <p
                                    class="text-[#67974e] text-sm font-normal leading-normal"
                                >
                                    Subtotal
                                </p>
                                <p
                                    class="text-[#121b0e] text-sm font-normal leading-normal"
                                >
                                    $12.00
                                </p>
                            </div>
                            <div
                                class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d7e7d0] py-5"
                            >
                                <p
                                    class="text-[#67974e] text-sm font-normal leading-normal"
                                >
                                    Tax
                                </p>
                                <p
                                    class="text-[#121b0e] text-sm font-normal leading-normal"
                                >
                                    $1.00
                                </p>
                            </div>
                            <div
                                class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d7e7d0] py-5"
                            >
                                <p
                                    class="text-[#67974e] text-sm font-normal leading-normal"
                                >
                                    Total
                                </p>
                                <p
                                    class="text-[#121b0e] text-sm font-normal leading-normal"
                                >
                                    $13.00
                                </p>
                            </div>
                        </div>
                        <div class="flex px-4 py-3">
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 flex-1 bg-[#4bb814] text-[#121b0e] text-sm font-bold leading-normal tracking-[0.015em]"
                            >
                                <span class="truncate">View Order Status</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>

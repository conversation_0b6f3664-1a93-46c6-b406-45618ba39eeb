<html>
    <head>
        <link
            rel="preconnect"
            href="https://fonts.gstatic.com/"
            crossorigin=""
        />
        <link
            rel="stylesheet"
            as="style"
            onload="this.rel='stylesheet'"
            href="https://fonts.googleapis.com/css2?display=swap&amp;family=Epilogue%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
        />

        <title>Stitch Design</title>
        <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

        <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    </head>
    <body>
        <div
            class="relative flex size-full min-h-screen flex-col bg-[#f9fcf8] group/design-root overflow-x-hidden"
            style="font-family: Epilogue, &quot;Noto Sans&quot;, sans-serif"
        >
            <div class="layout-container flex h-full grow flex-col">
                <header
                    class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#ebf3e7] px-10 py-3"
                >
                    <div class="flex items-center gap-4 text-[#121b0e]">
                        <div class="size-4">
                            <svg
                                viewBox="0 0 48 48"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M13.8261 30.5736C16.7203 29.8826 20.2244 29.4783 24 29.4783C27.7756 29.4783 31.2797 29.8826 34.1739 30.5736C36.9144 31.2278 39.9967 32.7669 41.3563 33.8352L24.8486 7.36089C24.4571 6.73303 23.5429 6.73303 23.1514 7.36089L6.64374 33.8352C8.00331 32.7669 11.0856 31.2278 13.8261 30.5736Z"
                                    fill="currentColor"
                                ></path>
                                <path
                                    fill-rule="evenodd"
                                    clip-rule="evenodd"
                                    d="M39.998 35.764C39.9944 35.7463 39.9875 35.7155 39.9748 35.6706C39.9436 35.5601 39.8949 35.4259 39.8346 35.2825C39.8168 35.2403 39.7989 35.1993 39.7813 35.1602C38.5103 34.2887 35.9788 33.0607 33.7095 32.5189C30.9875 31.8691 27.6413 31.4783 24 31.4783C20.3587 31.4783 17.0125 31.8691 14.2905 32.5189C12.0012 33.0654 9.44505 34.3104 8.18538 35.1832C8.17384 35.2075 8.16216 35.233 8.15052 35.2592C8.09919 35.3751 8.05721 35.4886 8.02977 35.589C8.00356 35.6848 8.00039 35.7333 8.00004 35.7388C8.00004 35.739 8 35.7393 8.00004 35.7388C8.00004 35.7641 8.0104 36.0767 8.68485 36.6314C9.34546 37.1746 10.4222 37.7531 11.9291 38.2772C14.9242 39.319 19.1919 40 24 40C28.8081 40 33.0758 39.319 36.0709 38.2772C37.5778 37.7531 38.6545 37.1746 39.3151 36.6314C39.9006 36.1499 39.9857 35.8511 39.998 35.764ZM4.95178 32.7688L21.4543 6.30267C22.6288 4.4191 25.3712 4.41909 26.5457 6.30267L43.0534 32.777C43.0709 32.8052 43.0878 32.8338 43.104 32.8629L41.3563 33.8352C43.104 32.8629 43.1038 32.8626 43.104 32.8629L43.1051 32.865L43.1065 32.8675L43.1101 32.8739L43.1199 32.8918C43.1276 32.906 43.1377 32.9246 43.1497 32.9473C43.1738 32.9925 43.2062 33.0545 43.244 33.1299C43.319 33.2792 43.4196 33.489 43.5217 33.7317C43.6901 34.1321 44 34.9311 44 35.7391C44 37.4427 43.003 38.7775 41.8558 39.7209C40.6947 40.6757 39.1354 41.4464 37.385 42.0552C33.8654 43.2794 29.133 44 24 44C18.867 44 14.1346 43.2794 10.615 42.0552C8.86463 41.4464 7.30529 40.6757 6.14419 39.7209C4.99695 38.7775 3.99999 37.4427 3.99999 35.7391C3.99999 34.8725 4.29264 34.0922 4.49321 33.6393C4.60375 33.3898 4.71348 33.1804 4.79687 33.0311C4.83898 32.9556 4.87547 32.8935 4.9035 32.8471C4.91754 32.8238 4.92954 32.8043 4.93916 32.7889L4.94662 32.777L4.95178 32.7688ZM35.9868 29.004L24 9.77997L12.0131 29.004C12.4661 28.8609 12.9179 28.7342 13.3617 28.6282C16.4281 27.8961 20.0901 27.4783 24 27.4783C27.9099 27.4783 31.5719 27.8961 34.6383 28.6282C35.082 28.7342 35.5339 28.8609 35.9868 29.004Z"
                                    fill="currentColor"
                                ></path>
                            </svg>
                        </div>
                        <h2
                            class="text-[#121b0e] text-lg font-bold leading-tight tracking-[-0.015em]"
                        >
                            Matcha Moments
                        </h2>
                    </div>
                    <div class="flex flex-1 justify-end gap-8">
                        <div class="flex items-center gap-9">
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Menu</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Order Online</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Catering</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >About Us</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Contact</a
                            >
                        </div>
                        <div class="flex gap-2">
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#ebf3e7] text-[#121b0e] text-sm font-bold leading-normal tracking-[0.015em]"
                            >
                                <span class="truncate">Log in</span>
                            </button>
                            <button
                                class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 bg-[#ebf3e7] text-[#121b0e] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
                            >
                                <div
                                    class="text-[#121b0e]"
                                    data-icon="MagnifyingGlass"
                                    data-size="20px"
                                    data-weight="regular"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="20px"
                                        height="20px"
                                        fill="currentColor"
                                        viewBox="0 0 256 256"
                                    >
                                        <path
                                            d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                                        ></path>
                                    </svg>
                                </div>
                            </button>
                            <button
                                class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 bg-[#ebf3e7] text-[#121b0e] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
                            >
                                <div
                                    class="text-[#121b0e]"
                                    data-icon="ShoppingBag"
                                    data-size="20px"
                                    data-weight="regular"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="20px"
                                        height="20px"
                                        fill="currentColor"
                                        viewBox="0 0 256 256"
                                    >
                                        <path
                                            d="M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm0,160H40V56H216V200ZM176,88a48,48,0,0,1-96,0,8,8,0,0,1,16,0,32,32,0,0,0,64,0,8,8,0,0,1,16,0Z"
                                        ></path>
                                    </svg>
                                </div>
                            </button>
                        </div>
                    </div>
                </header>
                <div class="px-40 flex flex-1 justify-center py-5">
                    <div
                        class="layout-content-container flex flex-col max-w-[960px] flex-1"
                    >
                        <div class="flex flex-wrap justify-between gap-3 p-4">
                            <p
                                class="text-[#121b0e] tracking-light text-[32px] font-bold leading-tight min-w-72"
                            >
                                About Us
                            </p>
                        </div>
                        <p
                            class="text-[#121b0e] text-base font-normal leading-normal pb-3 pt-1 px-4"
                        >
                            At Matcha Moments, we're passionate about bringing
                            the vibrant flavors and health benefits of matcha to
                            your everyday life. Our journey began with a simple
                            desire: to share the unique experience of
                            high-quality matcha with our community. From
                            sourcing the finest matcha powder to crafting
                            innovative recipes, we're committed to excellence in
                            every cup.
                        </p>
                        <h2
                            class="text-[#121b0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5"
                        >
                            Our Story
                        </h2>
                        <p
                            class="text-[#121b0e] text-base font-normal leading-normal pb-3 pt-1 px-4"
                        >
                            Founded in 2018, Matcha Moments started as a small
                            cafe in the heart of the city. We quickly gained a
                            loyal following for our signature matcha lattes,
                            smoothies, and baked goods. As demand grew, we
                            expanded our offerings to include online ordering
                            and delivery, making it easier than ever to enjoy
                            our matcha creations from the comfort of your home.
                        </p>
                        <h2
                            class="text-[#121b0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5"
                        >
                            Our Mission
                        </h2>
                        <p
                            class="text-[#121b0e] text-base font-normal leading-normal pb-3 pt-1 px-4"
                        >
                            Our mission is to provide a delightful and
                            accessible matcha experience for everyone. We
                            believe in using only the highest quality
                            ingredients, prepared with care and attention to
                            detail. We strive to create a welcoming atmosphere,
                            both in our cafe and online, where customers can
                            discover the versatility and goodness of matcha.
                        </p>
                        <h2
                            class="text-[#121b0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5"
                        >
                            Meet the Team
                        </h2>
                        <div
                            class="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4"
                        >
                            <div class="flex flex-col gap-3 text-center pb-3">
                                <div class="px-4">
                                    <div
                                        class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-full"
                                        style="
                                            background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuCsVtLwg99okJ-O4GgBkCkarmaEXAlmouiCldrOFFCdN7CMnfY2GvQtERJioX4SwZ92G0ROabt3W3D7x3nVRFp7N2iocyMzPaMLv_4kR5P37FllTaURFAQ9uCbbbmi33-gZXe_QKpIGiwxcEeRHlJ8dC0rdJX0BTDld18en3o1K2ZepUWG-B6ttJfLY-GzpTx90u-1IWkQIdPcicgtCBiTo75JMDrpjWugsYbTR-QTUC-2pGZKaSm_5wUUaupcs0-XQQYvJn76sZx0&quot;);
                                        "
                                    ></div>
                                </div>
                                <div>
                                    <p
                                        class="text-[#121b0e] text-base font-medium leading-normal"
                                    >
                                        Sarah Chen
                                    </p>
                                    <p
                                        class="text-[#67974e] text-sm font-normal leading-normal"
                                    >
                                        Founder &amp; CEO
                                    </p>
                                </div>
                            </div>
                            <div class="flex flex-col gap-3 text-center pb-3">
                                <div class="px-4">
                                    <div
                                        class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-full"
                                        style="
                                            background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuAR3e3laE3AvDHetyK0jFYzRMiEwtHvkUxOfPU99GiSVZwjCigMXaZxDClwQDEaGnpsPK9VRH3yOncDxDluVz-ntbrYfmYUID9XRTFMa4XOnlE99mLgoz0oetiNg0wujXX1DmG5qWqu2L5MjyCZefwNLUl4rvDOYH94g09-zmqqCy4vM2wjCb23Cd5R86X_fzwWrtRxpCBh_Fp-dlk_jiUsUj1HDa-bLiaXYSyQrpoR0taLQ1ixCjZ-NRf9H8b9Rywjin10MhKhJ-8&quot;);
                                        "
                                    ></div>
                                </div>
                                <div>
                                    <p
                                        class="text-[#121b0e] text-base font-medium leading-normal"
                                    >
                                        David Lee
                                    </p>
                                    <p
                                        class="text-[#67974e] text-sm font-normal leading-normal"
                                    >
                                        Head Barista
                                    </p>
                                </div>
                            </div>
                            <div class="flex flex-col gap-3 text-center pb-3">
                                <div class="px-4">
                                    <div
                                        class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-full"
                                        style="
                                            background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuBVpyX0AaKLjd0hvQ9y5csEKmTk-AGYAVnE9BANJh-pwA3YTl15rkH6MbZLTc9VIOzwtIj1a5i60YYg2wHvmcmG1htIDQYCy9foUA85DTRWi2MbOekdgPMS9xwS9XCVRysSWOh3yh6Sirep8zCijOnNiRlkfGrk-YvVcWBj1l8lw64I8t99rcSSKujU92-Nou9NZKoX4C5byKaHQW4XixXgn7TLOOAIXGPSdvLOrlLqTSfBNuvvUMfrZPZLQ0qRQliudfI0Dx0_8UY&quot;);
                                        "
                                    ></div>
                                </div>
                                <div>
                                    <p
                                        class="text-[#121b0e] text-base font-medium leading-normal"
                                    >
                                        Emily Wong
                                    </p>
                                    <p
                                        class="text-[#67974e] text-sm font-normal leading-normal"
                                    >
                                        Marketing Manager
                                    </p>
                                </div>
                            </div>
                        </div>
                        <h2
                            class="text-[#121b0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5"
                        >
                            Our Commitment to Quality
                        </h2>
                        <p
                            class="text-[#121b0e] text-base font-normal leading-normal pb-3 pt-1 px-4"
                        >
                            We source our matcha directly from renowned tea
                            farms, ensuring the highest quality and freshness.
                            Our matcha is carefully selected for its vibrant
                            color, smooth texture, and rich flavor profile.
                            We're dedicated to sustainable practices and ethical
                            sourcing, supporting the communities that cultivate
                            our exceptional matcha.
                        </p>
                        <div class="flex px-4 py-3 justify-start">
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#4bb814] text-[#121b0e] text-sm font-bold leading-normal tracking-[0.015em]"
                            >
                                <span class="truncate">Contact Us</span>
                            </button>
                        </div>
                    </div>
                </div>
                <footer class="flex justify-center">
                    <div class="flex max-w-[960px] flex-1 flex-col">
                        <footer
                            class="flex flex-col gap-6 px-5 py-10 text-center @container"
                        >
                            <div
                                class="flex flex-wrap items-center justify-center gap-6 @[480px]:flex-row @[480px]:justify-around"
                            >
                                <a
                                    class="text-[#67974e] text-base font-normal leading-normal min-w-40"
                                    href="#"
                                    >Privacy Policy</a
                                >
                                <a
                                    class="text-[#67974e] text-base font-normal leading-normal min-w-40"
                                    href="#"
                                    >Terms of Service</a
                                >
                            </div>
                            <p
                                class="text-[#67974e] text-base font-normal leading-normal"
                            >
                                @2023 Matcha Moments. All rights reserved.
                            </p>
                        </footer>
                    </div>
                </footer>
            </div>
        </div>
    </body>
</html>

<html>
    <head>
        <link
            rel="preconnect"
            href="https://fonts.gstatic.com/"
            crossorigin=""
        />
        <link
            rel="stylesheet"
            as="style"
            onload="this.rel='stylesheet'"
            href="https://fonts.googleapis.com/css2?display=swap&amp;family=Epilogue%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
        />

        <title>Stitch Design</title>
        <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

        <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    </head>
    <body>
        <div
            class="relative flex size-full min-h-screen flex-col bg-[#f9fcf8] group/design-root overflow-x-hidden"
            style="
                --radio-dot-svg: url(&quot;data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27rgb(75,184,20)%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e&quot;);
                font-family: Epilogue, &quot;Noto Sans&quot;, sans-serif;
            "
        >
            <div class="layout-container flex h-full grow flex-col">
                <header
                    class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#ebf3e7] px-10 py-3"
                >
                    <div class="flex items-center gap-4 text-[#121b0e]">
                        <div class="size-4">
                            <svg
                                viewBox="0 0 48 48"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <g clip-path="url(#clip0_6_535)">
                                    <path
                                        fill-rule="evenodd"
                                        clip-rule="evenodd"
                                        d="M47.2426 24L24 47.2426L0.757355 24L24 0.757355L47.2426 24ZM12.2426 21H35.7574L24 9.24264L12.2426 21Z"
                                        fill="currentColor"
                                    ></path>
                                </g>
                                <defs>
                                    <clipPath id="clip0_6_535">
                                        <rect
                                            width="48"
                                            height="48"
                                            fill="white"
                                        ></rect>
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <h2
                            class="text-[#121b0e] text-lg font-bold leading-tight tracking-[-0.015em]"
                        >
                            Matcha Cafe
                        </h2>
                    </div>
                    <div class="flex flex-1 justify-end gap-8">
                        <div class="flex items-center gap-9">
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Menu</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Catering</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >About</a
                            >
                            <a
                                class="text-[#121b0e] text-sm font-medium leading-normal"
                                href="#"
                                >Contact</a
                            >
                        </div>
                        <button
                            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#ebf3e7] text-[#121b0e] text-sm font-bold leading-normal tracking-[0.015em]"
                        >
                            <span class="truncate">Order Online</span>
                        </button>
                        <div
                            class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                            style="
                                background-image: url(&quot;https://lh3.googleusercontent.com/aida-public/AB6AXuDVJHSeQDVQ6iMLOQ8Bcg5HMsHiv4Q7O1qcuKNJPo2mwY6ZlngahHgvsbAB8NAGJyf2FGUU-5enA8cVVUSY7PABE6C0BexdeoRHcMPeLTiFb9zvNqTW0EtsAabtTZ9vsBEPYBHK-NaBeqYoQhXGB7Y-6ILltBT_cwsKkOvL1-NJWciOaAlRsJIvxKThyXZAV3qZgxq_qrM2S00c3hgEJMDbF4QqmqtZf_lZq0APafIn0ZKnLx9yjhLkHvSig6WsNOldVLOurZeK8-E&quot;);
                            "
                        ></div>
                    </div>
                </header>
                <div class="px-40 flex flex-1 justify-center py-5">
                    <div
                        class="layout-content-container flex flex-col w-[512px] max-w-[512px] py-5 max-w-[960px] flex-1"
                    >
                        <div class="flex flex-wrap justify-between gap-3 p-4">
                            <p
                                class="text-[#121b0e] tracking-light text-[32px] font-bold leading-tight min-w-72"
                            >
                                Checkout
                            </p>
                        </div>
                        <h3
                            class="text-[#121b0e] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4"
                        >
                            Delivery Address
                        </h3>
                        <div
                            class="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3"
                        >
                            <label class="flex flex-col min-w-40 flex-1">
                                <input
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#121b0e] focus:outline-0 focus:ring-0 border border-[#d7e7d0] bg-[#f9fcf8] focus:border-[#d7e7d0] h-14 placeholder:text-[#67974e] p-[15px] text-base font-normal leading-normal"
                                    value=""
                                />
                            </label>
                        </div>
                        <div
                            class="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3"
                        >
                            <label class="flex flex-col min-w-40 flex-1">
                                <input
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#121b0e] focus:outline-0 focus:ring-0 border border-[#d7e7d0] bg-[#f9fcf8] focus:border-[#d7e7d0] h-14 placeholder:text-[#67974e] p-[15px] text-base font-normal leading-normal"
                                    value=""
                                />
                            </label>
                        </div>
                        <div
                            class="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3"
                        >
                            <label class="flex flex-col min-w-40 flex-1">
                                <input
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#121b0e] focus:outline-0 focus:ring-0 border border-[#d7e7d0] bg-[#f9fcf8] focus:border-[#d7e7d0] h-14 placeholder:text-[#67974e] p-[15px] text-base font-normal leading-normal"
                                    value=""
                                />
                            </label>
                            <label class="flex flex-col min-w-40 flex-1">
                                <input
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#121b0e] focus:outline-0 focus:ring-0 border border-[#d7e7d0] bg-[#f9fcf8] focus:border-[#d7e7d0] h-14 placeholder:text-[#67974e] p-[15px] text-base font-normal leading-normal"
                                    value=""
                                />
                            </label>
                        </div>
                        <h3
                            class="text-[#121b0e] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4"
                        >
                            Payment Method
                        </h3>
                        <div class="flex flex-col gap-3 p-4">
                            <label
                                class="flex items-center gap-4 rounded-xl border border-solid border-[#d7e7d0] p-[15px]"
                            >
                                <input
                                    type="radio"
                                    class="h-5 w-5 border-2 border-[#d7e7d0] bg-transparent text-transparent checked:border-[#4bb814] checked:bg-[image:--radio-dot-svg] focus:outline-none focus:ring-0 focus:ring-offset-0 checked:focus:border-[#4bb814]"
                                    name="b3492456-b312-42fe-a23d-1b9f7d065060"
                                    checked=""
                                />
                                <div class="flex grow flex-col">
                                    <p
                                        class="text-[#121b0e] text-sm font-medium leading-normal"
                                    >
                                        Credit Card
                                    </p>
                                </div>
                            </label>
                            <label
                                class="flex items-center gap-4 rounded-xl border border-solid border-[#d7e7d0] p-[15px]"
                            >
                                <input
                                    type="radio"
                                    class="h-5 w-5 border-2 border-[#d7e7d0] bg-transparent text-transparent checked:border-[#4bb814] checked:bg-[image:--radio-dot-svg] focus:outline-none focus:ring-0 focus:ring-offset-0 checked:focus:border-[#4bb814]"
                                    name="b3492456-b312-42fe-a23d-1b9f7d065060"
                                />
                                <div class="flex grow flex-col">
                                    <p
                                        class="text-[#121b0e] text-sm font-medium leading-normal"
                                    >
                                        Cash on Delivery
                                    </p>
                                </div>
                            </label>
                        </div>
                        <div
                            class="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3"
                        >
                            <label class="flex flex-col min-w-40 flex-1">
                                <input
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#121b0e] focus:outline-0 focus:ring-0 border border-[#d7e7d0] bg-[#f9fcf8] focus:border-[#d7e7d0] h-14 placeholder:text-[#67974e] p-[15px] text-base font-normal leading-normal"
                                    value=""
                                />
                            </label>
                        </div>
                        <div
                            class="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3"
                        >
                            <label class="flex flex-col min-w-40 flex-1">
                                <input
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#121b0e] focus:outline-0 focus:ring-0 border border-[#d7e7d0] bg-[#f9fcf8] focus:border-[#d7e7d0] h-14 placeholder:text-[#67974e] p-[15px] text-base font-normal leading-normal"
                                    value=""
                                />
                            </label>
                            <label class="flex flex-col min-w-40 flex-1">
                                <input
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#121b0e] focus:outline-0 focus:ring-0 border border-[#d7e7d0] bg-[#f9fcf8] focus:border-[#d7e7d0] h-14 placeholder:text-[#67974e] p-[15px] text-base font-normal leading-normal"
                                    value=""
                                />
                            </label>
                        </div>
                        <div class="flex px-4 py-3">
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 flex-1 bg-[#4bb814] text-[#121b0e] text-sm font-bold leading-normal tracking-[0.015em]"
                            >
                                <span class="truncate">Place Order</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>

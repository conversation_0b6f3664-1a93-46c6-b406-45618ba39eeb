@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Outfit:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');
@import "tailwindcss";

:root {
    /* Modern Color Palette */
        --color-primary-50: #f0fdf4;
        --color-primary-100: #dcfce7;
        --color-primary-200: #bbf7d0;
        --color-primary-300: #86efac;
        --color-primary-400: #4ade80;
        --color-primary-500: #22c55e;
        --color-primary-600: #16a34a;
        --color-primary-700: #15803d;
        --color-primary-800: #166534;
        --color-primary-900: #14532d;
        --color-primary-950: #052e16;
        /* Modern Matcha Colors */
        --color-matcha-50: #f6fdf7;
        --color-matcha-100: #e8faea;
        --color-matcha-200: #d1f4d6;
        --color-matcha-300: #a7e8b0;
        --color-matcha-400: #75d482;
        --color-matcha-500: #4ade80;
        --color-matcha-600: #22c55e;
        --color-matcha-700: #16a34a;
        --color-matcha-800: #15803d;
        --color-matcha-900: #14532d;
        /* Modern Neutrals */
        --color-neutral-50: #fafafa;
        --color-neutral-100: #f5f5f5;
        --color-neutral-200: #e5e5e5;
        --color-neutral-300: #d4d4d4;
        --color-neutral-400: #a3a3a3;
        --color-neutral-500: #737373;
        --color-neutral-600: #525252;
        --color-neutral-700: #404040;
        --color-neutral-800: #262626;
        --color-neutral-900: #171717;
        --color-neutral-950: #0a0a0a;
        /* Modern Accent Colors */
        --color-sage-50: #f6f7f6;
        --color-sage-100: #e8ebe8;
        --color-sage-200: #d1d8d1;
        --color-sage-300: #aab8aa;
        --color-sage-400: #7d927d;
        --color-sage-500: #5f7a5f;
        --color-sage-600: #4a614a;
        --color-sage-700: #3d4f3d;
        --color-sage-800: #334133;
        --color-sage-900: #2c372c;
        /* Modern Shadows */
        --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
        --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
        /* Modern Glass Effects */
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --glass-backdrop: blur(12px);
        /* Modern Spacing Scale */
        --space-px: 1px;
        --space-0: 0px;
        --space-1: 0.25rem;
        --space-2: 0.5rem;
        --space-3: 0.75rem;
        --space-4: 1rem;
        --space-5: 1.25rem;
        --space-6: 1.5rem;
        --space-8: 2rem;
        --space-10: 2.5rem;
        --space-12: 3rem;
        --space-16: 4rem;
        --space-20: 5rem;
        --space-24: 6rem;
        --space-32: 8rem;
        /* Modern Border Radius */
        --radius-none: 0px;
        --radius-sm: 0.125rem;
        --radius-md: 0.375rem;
        --radius-lg: 0.5rem;
        --radius-xl: 0.75rem;
        --radius-2xl: 1rem;
        --radius-3xl: 1.5rem;
        --radius-full: 9999px;
}

/* Modern Base Styles */
* {
    margin: 0;
        padding: 0;
        box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

body {
    font-family: 'Inter', system-ui, sans-serif;
        background: var(--color-neutral-50);
        color: var(--color-neutral-900);
        line-height: 1.6;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'rlig' 1, 'calt' 1;
}

/* Modern Typography Scale */
.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}
.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}
.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
}
.text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
}
.text-5xl {
    font-size: 3rem;
    line-height: 1;
}

.text-6xl {
    font-size: 3.75rem;
    line-height: 1;
}

.text-7xl {
    font-size: 4.5rem;
    line-height: 1;
}

.text-8xl {
    font-size: 6rem;
    line-height: 1;
}

.text-9xl {
    font-size: 8rem;
    line-height: 1;
}
/* Modern Display Typography */
.text-display-sm {
    font-size: clamp(2.25rem, 4vw, 3rem);
    line-height: 1.1;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.text-display-md {
    font-size: clamp(3rem, 6vw, 4.5rem);
    line-height: 1.1;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.text-display-lg {
    font-size: clamp(4.5rem, 8vw, 7rem);
    line-height: 1;
    font-weight: 800;
    letter-spacing: -0.025em;
}

/* Modern Font Families */
.font-display {
    font-family: 'Outfit', system-ui, sans-serif;
}

.font-body {
    font-family: 'Inter', system-ui, sans-serif;
}

.font-serif {
    font-family: 'Playfair Display', Georgia, serif;
}

/* Modern Button Styles */
.btn {
    display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    font-weight: 500;
        font-size: 0.875rem;
    line-height: 1.25rem;
        border-radius: var(--radius-lg);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        border: none;
        text-decoration: none;
        position: relative;
        overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-matcha-500), var(--color-matcha-600));
        color: white;
    padding: 0.75rem 1.5rem;
        box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--color-matcha-600), var(--color-matcha-700));
        box-shadow: var(--shadow-md);
        transform: translateY(-1px);
}

.btn-secondary {
    background: var(--color-neutral-50);
        color: var(--color-neutral-900);
        border: 1px solid var(--color-neutral-200);
        padding: 0.75rem 1.5rem;
        box-shadow: var(--shadow-xs);
}

.btn-secondary:hover {
    background: var(--color-neutral-100);
        border-color: var(--color-neutral-300);
        box-shadow: var(--shadow-sm);
}

.btn-ghost {
    background: transparent;
    color: var(--color-neutral-700);
        padding: 0.75rem 1.5rem;
}

.btn-ghost:hover {
    background: var(--color-neutral-100);
        color: var(--color-neutral-900);
}

/* Modern Card Styles */
.card {
    background: var(--color-neutral-50);
        border: 1px solid var(--color-neutral-200);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-sm);
        overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
    box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
        border-color: var(--color-neutral-300);
}

.card-glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

/* Modern Container */
.container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .container {
        max-width: 640px;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 768px;
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 1024px;
}
@media (min-width: 1280px) {
    .container {
        max-width: 1280px;
    
}
@media (min-width: 1536px) {
    .container {
        max-width: 1536px;
    }
}

/* Modern Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
        
            to {
                opacity: 1;
                transform: translateY(0);
    
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
        
            to {
        opacity: 1;
        transform: scale(1);
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
        
            to {
        opacity: 1;
        transform: translateX(0);
    
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
        
            to {
        opacity: 1;
        transform: translateX(0);
    
}

@keyframes float {
    0%,
        100% {
            transform: translateY(0px);
        }
                50% {
        transform: translateY(-10px);
        }
}

@keyframes pulse {
    0%,
        100% {
        opacity: 1;
        }
                50% {
        opacity: 0.8;
        }
}
}

                                                                                                                                                                                                                                                                /* Modern Animation Classes */
                                                                                                                                                                                                                                                                .animate-fade-in-up {
                                                                                                                                                                                                                                                                    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                .animate-fade-in-scale {
                                                                                                                                                                                                                                                                    animation: fadeInScale 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                .animate-slide-in-left {
            animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            }

                                                                                                                                                                                                                                                                .animate-slide-in-right {
            animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            }

                                                                                                                                                                                                                                                                .animate-float {
            animation: float 3s ease-in-out infinite;
            }
            
            .animate-pulse-slow {
                animation: pulse 3s ease-in-out infinite;
            }
                                                                                                                                                                                                                                                                /* Modern Hover Effects */
                                                                                                                                                                                                                                                                .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
            
            .hover-lift:hover {
                transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            }
            
            .hover-scale {
            transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            }
            
            .hover-scale:hover {
                transform: scale(1.05);
            }

                                                                                                                                                                                                                                                                .hover-glow {
                                                                                                                                                                                                                                                                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                .hover-glow:hover {
                                                                                                                                                                                                                                                                    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                /* Modern Utility Classes */
                                                                                                                                                                                                                                                                .glass {
                                                                                                                                                                                                                                                                    background: var(--glass-bg);
                                                                                                                                                                                                                                                                    backdrop-filter: var(--glass-backdrop);
                                                                                                                                                                                                                                                                    border: 1px solid var(--glass-border);
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                .gradient-primary {
                                                                                                                                                                                                                                                                    background: linear-gradient(135deg, var(--color-matcha-400), var(--color-matcha-600));
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                .gradient-sage {
                                                                                                                                                                                                                                                                    background: linear-gradient(135deg, var(--color-sage-300), var(--color-sage-500));
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                .gradient-text {
                                                                                                                                                                                                                                                                    background: linear-gradient(135deg, var(--color-matcha-500), var(--color-matcha-700));
                                                                                                                                                                                                                                                                    -webkit-background-clip: text;
                                                                                                                                                                                                                                                                    -webkit-text-fill-color: transparent;
                                                                                                                                                                                                                                                                    background-clip: text;
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                /* Modern Focus States */
                                                                                                                                                                                                                                                                .focus-visible:focus-visible {
                                                                                                                                                                                                                                                                    outline: 2px solid var(--color-matcha-500);
                                                                                                                                                                                                                                                                    outline-offset: 2px;
                                                                                                                                                                                                                                                                    border-radius: var(--radius-md);
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                /* Modern Scrollbar */
                                                                                                                                                                                                                                                                ::-webkit-scrollbar {
                                                                                                                                                                                                                                                                    width: 8px;
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                ::-webkit-scrollbar-track {
                                                                                                                                                                                                                                                                    background: var(--color-neutral-100);
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                ::-webkit-scrollbar-thumb {
                                                                                                                                                                                                                                                                    background: var(--color-neutral-300);
                                                                                                                                                                                                                                                                    border-radius: var(--radius-full);
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                ::-webkit-scrollbar-thumb:hover {
                                                                                                                                                                                                                                                                    background: var(--color-neutral-400);
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                /* Modern Selection */
                                                                                                                                                                                                                                                                ::selection {
                                                                                                                                                                                                                                                                    background: var(--color-matcha-200);
                                                                                                                                                                                                                                                                    color: var(--color-matcha-900);
                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                /* Modern Print Styles */
                                                                                                                                                                                                                                                                @media print {
                                                                                                                                                                                                                                                                    * {
                                                                                                                                                                                                                                                                        background: transparent !important;
                                                                                                                                                                                                                                                                        color: black !important;
                                                                                                                                                                                                                                                                        box-shadow: none !important;
                                                                                                                                                                                                                                                                        text-shadow: none !important;
                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                }
import Image from 'next/image';

export default function HomePage() {
  return (
    <div className="bg-white">
      <header className="relative h-screen text-white overflow-hidden">
        <div className="absolute inset-0">
          <Image
            alt="Matcha being the olyncha"
            src="https://lh3.googleusercontent.com/aida-public/AB6AXuAJuESxaKIQMjctTM0p2kzRM_3KRrpMUzv9h8tVNFsMlXG7ViaJ2HlCy_lftswdIXPwW00tHkGB3tD54n8Pq9oG12pmvngtQpErSSZ_pluv5OviPsPnEuKj4hAtEGnwdJ1ptccXoY8OjCPf-lJi_jDSY2GM0GYl_WajEDA0ccIfSZfq1C-haN73tv0oLQewdaimJLFe_SExTBkPJCuMjQPDa6t49CGZYy9zzQm7hQ9-D00lb0_NVkV4Hme1VQuBfcr43sLSOnuuaiU"
            layout="fill"
            objectFit="cover"
          />
        </div>
        <div className="absolute inset-0 bg-black opacity-30"></div>
        <div className="relative z-10 p-6 md:p-12 max-w-7xl mx-auto">
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center transition-transform duration-300 hover:rotate-[-2deg]">
            <h1 className="font-playfair text-6xl md:text-9xl leading-none">
              olyncha
            </h1>
            <p className="uppercase text-sm tracking-widest mt-2">
              enjoy matcha
              <br />
              with olyncha
            </p>
          </div>
        </div>
      </header>
      <main>
        <section className="grid grid-cols-1 md:grid-cols-2">
          <div className="bg-gray-100 p-8 md:p-16 flex items-center justify-center overflow-hidden">
            <Image
              alt="Animated character olyncha matcha"
              className="max-w-xs w-full transition-transform duration-500 hover:scale-110 hover:rotate-3"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuAiAuphwsGqtc-fnNsD0gxfc_8Jcl-FWLlAKefwa9FCeQXpiovIBat_zS-MOeu2Q4NlzkmcuRr5RL3sIl97f-QoAdBb62b0N9kCaWxzboffNvYAjg1zt3AYOTF_q0SzwgilztE3AXEeW4-cy6rox_rcHxvmWVaZrvC3rCxsreI0jqOw7YDBzJE2hiRjdPxso9KnlrlY8lW5JDs4kTAb9XSPzftxLrX1QFw2c8bs8x0zYGVIgVI0MEFy4ZA283_GoLQQBPFnNX-v9V8"
              width={400}
              height={400}
            />
          </div>
          <div className="bg-stone-100 p-8 md:p-16 flex items-center justify-center overflow-hidden">
            <Image
              alt="Glass of sans matcha"
              className="max-w-xs w-full transition-transform duration-500 hover:scale-110 hover:-rotate-3"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuC7no3v_suebCORqhLJo6JAANgQaguQOs_Ajp5KMi5yLW9VsM_uX3soQl7LIxvY1H9R1lmk8AU7RhcqnnqfNBMuXfk0ige2LCfb65gQRuuEizt0CkYc5EVrRgpOL3A1fJHxeuKkSKp46f9hxqrSFWPk40ej9b4z4nGd5ACAVIzseJ_oa_vnG85xw41fpcr1dV4iQ-B30ex2Cav8LeA-mWDMu7UG1O3D8-H3v6OVme2q4pfr2YVugzd3TpxxqAbFDuUTqzy4V8BF5A4"
              width={400}
              height={400}
            />
          </div>
        </section>
        <nav className="py-6 border-b border-t border-gray-200">
          <ul className="flex justify-center space-x-8 text-xs uppercase tracking-widest text-gray-500">
            <li>
              <a className="hover:text-black hover:font-bold transition-all" href="#">
                Signature Menu
              </a>
            </li>
            <li>
              <a className="hover:text-black hover:font-bold transition-all" href="#">
                Matcha Powder
              </a>
            </li>
            <li>
              <a className="hover:text-black hover:font-bold transition-all" href="#">
                Matcha Kit
              </a>
            </li>
            <li>
              <a className="hover:text-black hover:font-bold transition-all" href="#">
                Sans at Events
              </a>
            </li>
            <li>
              <a className="hover:text-black hover:font-bold transition-all" href="#">
                Merchandise
              </a>
            </li>
          </ul>
        </nav>
        <section className="text-center py-20 px-4 bg-matcha-light">
          <h2 className="text-5xl md:text-7xl font-playfair mb-12 transition-transform duration-300 hover:scale-105">
            convenience
          </h2>
          <div className="relative inline-block">
            <Image
              alt="Cute matcha character"
              className="w-48 mx-auto float-animation"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuAClld0Odv1nu9_L58lh-rNMW_C6R1MmfRuDtR1z7J_s8i_5dqaUYfEnYv1VLhqe6bWqIQhplyZt3w5-5fpMj0suJTQ9SN9CTr64vDYoqhqC82T1-hzvT2evYHdtkxJ6ObORHzZOXgpU6s0U3rVqBWviftfx2eyJ2lM3avEqaQBd4GLqHZISWQP0HlmL9sSYiXZG6Nk9Vnh1-HRT5Y-WXlJ7w_8J2eUHpL9TdYL-XoCAZNJP7lkC43TgYRhM8TfdJCkb3y0l3_EW7Q"
              width={192}
              height={192}
            />
            <div className="hidden md:block absolute top-1/4 -left-32">
              <button className="bg-lime-300 text-black text-xs font-bold py-2 px-4 rounded-full transition-all duration-300 hover:bg-lime-400 hover:scale-110 hover:shadow-lg">
                LIKE MATCHA
              </button>
            </div>
            <div className="hidden md:block absolute top-1/2 -left-48 mt-4">
              <button className="bg-white border border-gray-300 text-gray-700 text-xs font-bold py-2 px-4 rounded-full transition-all duration-300 hover:bg-gray-100 hover:scale-110 hover:shadow-lg">
                MATCHA IN NEW WAVE
              </button>
            </div>
            <div className="hidden md:block absolute top-1/2 -right-48 mt-4">
              <button className="bg-lime-300 text-black text-xs font-bold py-2 px-4 rounded-full transition-all duration-300 hover:bg-lime-400 hover:scale-110 hover:shadow-lg">
                MATCHA POWDER
              </button>
            </div>
            <div className="hidden md:block absolute top-1/4 -right-32">
              <button className="bg-red-400 text-white text-xs font-bold py-2 px-4 rounded-full transition-all duration-300 hover:bg-red-500 hover:scale-110 hover:shadow-lg">
                MATCHA COOKIES
              </button>
            </div>
          </div>
          <p className="max-w-md mx-auto text-gray-600 mt-24">
            We’re not just selling matcha — we&apos;re delivering an experience you can see, feel and trap our taste right in your home
          </p>
          <div className="mt-8 flex justify-center space-x-4">
            <button className="bg-green-700 text-white py-3 px-6 rounded-full text-sm font-semibold transition-transform duration-300 hover:bg-green-800 hover:scale-105">
              SHOP CONVENIENT
            </button>
          </div>
        </section>
        <nav className="py-6 border-b border-t border-gray-200">
          <ul className="flex justify-center space-x-8 text-xs uppercase tracking-widest text-gray-500">
            <li>
              <a className="hover:text-black hover:font-bold transition-all" href="#">
                Signature Menu
              </a>
            </li>
            <li>
              <a className="hover:text-black hover:font-bold transition-all" href="#">
                Matcha Powder
              </a>
            </li>
            <li>
              <a className="hover:text-black hover:font-bold transition-all" href="#">
                Matcha Kit
              </a>
            </li>
            <li>
              <a className="hover:text-black hover:font-bold transition-all" href="#">
                Sans at Events
              </a>
            </li>
            <li>
              <a className="hover:text-black hover:font-bold transition-all" href="#">
                Merchandise
              </a>
            </li>
          </ul>
        </nav>
        <section className="py-20 px-4">
          <div className="text-center">
            <Image
              alt="SD Menu"
              className="mx-auto w-auto h-24 transition-transform duration-500 hover:rotate-[360deg]"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuBoB7ZZofh9StCR0BB0kh17LeDT-A62MaelrF7gkuPCs-LS8NVy80zYkiO9T2gfib-RCUO4tONuH8KPEsrD0dEplUv3Z8GPKwl2xny6Zvfidl3-i1XnqHFNQJRUpmq1bPsOYNcrY0xEZiMWYial-PxWipmnRDNYXE-VA8V9mrRXycAM1jAbhE1hq7X_P74NRlDPpSFzlnUBwL08CINPLTYSpd-l8GdXbnRv8HQWv45EewJ0e985yJeJzPSlOrkuiVZjoBJq_e4yv7I"
              width={96}
              height={96}
            />
            <button className="mt-8 bg-red-400 text-white py-2 px-5 rounded-full text-xs font-bold transition-all duration-300 hover:bg-red-500 hover:scale-110">
              CONVENIENT PRODUCTS
            </button>
          </div>
          <div className="mt-16 max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8 px-2 md:px-0">
            <div className="text-left group">
              <div className="flex justify-between items-baseline">
                <span className="text-xs text-gray-500">01</span>
                <span className="text-xs text-gray-500">DRINKS</span>
              </div>
              <div className="overflow-hidden rounded-lg my-4">
                <Image
                  alt="Strawberry Matcha drink"
                  className="w-full rounded-lg transition-transform duration-300 group-hover:scale-110"
                  src="https://lh3.googleusercontent.com/aida-public/AB6AXuBO5lkjPexV2Gk-ttrsv04h9qb3_uhnbZqN_OxKf__SC4NaWFRaekAGGLp6tOmGnpriy0Dx4PPFBBs4gXp5XWbBIvD2yPd_Z8m0DL5aTqaU121CASk4KPHdHDPhZuoXNPOhGY9SvHhhX_vvNfVNHvCAjVRzUxKS2JrW0DZKgmh8W4qVnTut9RwEZsD4h1oZCv03huuecPwyilK_ablp_3P3fQ40agKk95hWSLOkyJaliR8_VJtLr-Z-kYB7p1UEhBqQakneoy5ODCY"
                  width={500}
                  height={500}
                />
              </div>
              <div className="flex justify-between items-center">
                <h3 className="font-bold">STRAWBERRY MATCHA</h3>
                <span className="font-bold text-lg">$3.5</span>
              </div>
              <p className="text-gray-500 text-sm">
                Strawberry puree, premium matcha
              </p>
            </div>
            <div className="text-left group">
              <div className="flex justify-between items-baseline">
                <span className="text-xs text-gray-500">02</span>
                <span className="text-xs text-gray-500">DRINKS</span>
              </div>
              <div className="overflow-hidden rounded-lg my-4">
                <Image
                  alt="Matcha Latte"
                  className="w-full rounded-lg transition-transform duration-300 group-hover:scale-110"
                  src="https://lh3.googleusercontent.com/aida-public/AB6AXuCzUgn7hICSgCEODY1ixh2d8HDIMHb1pBVhD30-sz72N6GaQ7mjY15--IgYxx8uk2a6lTw9NUdkIehKbP70dJ_jdNDMQxHcrWv6tNn6YtYpd0W8oHpmDc2mwWnV4wlZVkopDz7j_ilC11A5FF3S2bUray0SF5jWba9OnPDdQHmuKK7CsTdq8hrq95A1CHqI1TSTMukrB4z99Ss8NaakTbKBAcOd_sB7JblbfsMnaSIR847hjnrbKNgZrokgPGivMf17yAti6IcA4MI"
                  width={500}
                  height={500}
                />
              </div>
              <div className="flex justify-between items-center">
                <h3 className="font-bold">MATCHA LATTE</h3>
                <span className="font-bold text-lg">$3.5</span>
              </div>
              <p className="text-gray-500 text-sm">
                Our signature premium matcha desserts
              </p>
            </div>
            <div className="text-left group">
              <div className="flex justify-between items-baseline">
                <span className="text-xs text-gray-500">03</span>
                <span className="text-xs text-gray-500">DRINKS</span>
              </div>
              <div className="overflow-hidden rounded-lg my-4">
                <Image
                  alt="Matcha Lemonade"
                  className="w-full rounded-lg transition-transform duration-300 group-hover:scale-110"
                  src="https://lh3.googleusercontent.com/aida-public/AB6AXuCt9mfMREhmNrAcNIxQw0GFRoJCfM5nxcH6e70gbGEdzBTQHlyHImUW3McOXsqFI0wLeYXOlycIWu8rY5phOI8uFu7jSuTchLC1iJizsEhgta7GRXYDdxvtUdQVaVnzm2TB6fxhmD9aorQ4VdfwRPc8xKkE6BFwlimmo5yNMXD3PEOW5EpHM3KANtyTKhoA8ESICTazua9L-Z9tpVeqa5ZYbkytpz1ga6WRi9KEGFs3SUBnECTJELYs9036_XJGY3eqiS_jy-SAslc"
                  width={500}
                  height={500}
                />
              </div>
              <div className="flex justify-between items-center">
                <h3 className="font-bold">MATCHA LEMONADE</h3>
                <span className="font-bold text-lg">$3.5</span>
              </div>
              <p className="text-gray-500 text-sm">
                Refreshing lemonade with a matcha shot
              </p>
            </div>
          </div>
          <div className="mt-20 max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8 text-center px-2 md:px-0">
            <div className="group">
              <Image
                alt="Premium matcha icon"
                className="mx-auto h-20 transition-transform duration-300 group-hover:animate-bounce"
                src="https://lh3.googleusercontent.com/aida-public/AB6AXuD1QwVuChL2b5KIG1zaPVk04bj3nd_S73vLxAIuvuq8R0qawncM0z3t6hCSxMtQxsr4VROJ3tCNvd6QdqSE9Vr0prJe-JRRJ9tYF5R-O-9YfstCPY8p50mTBeGiJHJDrKtdZx2wbcghgyKTLMZ51NkbrkoLHW7sDYd0FZ3nu57bhZxiNBjfehHIzN_OPwFMH4FWmIZ_2KNDiUoa7I5SiJFo1_oaaydDclD9XiInj75yzuIW_lvNh71A86WGiIdZ3hBY85WTWJO16UY"
                width={80}
                height={80}
              />
              <h4 className="font-bold mt-4">PREMIUM MATCHA</h4>
              <p className="text-gray-500 text-sm">
                The best premium matcha from Uji, Kyoto
              </p>
            </div>
            <div className="group">
              <Image
                alt="Spill proof icon"
                className="mx-auto h-20 transition-transform duration-300 group-hover:animate-bounce"
                src="https://lh3.googleusercontent.com/aida-public/AB6AXuB3S5D26N4Ax-lPIeqwagcV-aPXGyMegn1Zx09QH3iyBP1YbH6UI0052FoyJ2e-LyAsTioyk6Q6Ul4bfEae2yV-UsFMDJ4E1Z4UvHet10fJ50vcaINQYzoYKUADK8QFuyJWpS-DgPlKDyKO700acERx8yRl5-O9gIrEjKIZDjDcgVojiqX83cnMSnyyisRMd1-shq6WkXdDejARSqSBUkI_eIVDGoOuLZexjJzvss_uvUrsd1TX2t75y011sM0mlvIm1k_hMSmvKqQ"
                width={80}
                height={80}
              />
              <h4 className="font-bold mt-4">SPILL PROOF</h4>
              <p className="text-gray-500 text-sm">
                Spill-proof packaging without a single
                <br />
                plastic waste
              </p>
            </div>
            <div className="group">
              <Image
                alt="Fast delivery icon"
                className="mx-auto h-20 transition-transform duration-300 group-hover:animate-bounce"
                src="https://lh3.googleusercontent.com/aida-public/AB6AXuDlp0wrG50ZyngEJ2P1sQV6NFsi9u-rH_Vcj1CGhDMZ3kN698x-K1ZMVNVOeqVPWjMO5IIio2TM932dixea2mti-MvyjUX9aYKWC1hJgYPw1DTYkHyVtcbnKJF0etnoMBoFRDbIeXSqlmQhMA9woY5zsg-h2d7t0UNBcErtzoiejjhZcytGPh0y73jQbQz-9HCgiDfeACc47PUVBZxImrCo08Dyfqo1oqq--NuVVZNxRVAv522JbLgu0ql9ffVk5pLkXQeDsXi1Nvs"
                width={80}
                height={80}
              />
              <h4 className="font-bold mt-4">FAST AF DELIVERY</h4>
              <p className="text-gray-500 text-sm">
                We&apos;ll get your matcha to you in a flash
              </p>
            </div>
          </div>
        </section>
        <section className="bg-matcha-light py-20 px-4">
          <h2 className="text-5xl md:text-8xl font-playfair text-center mb-12 transition-transform duration-300 hover:tracking-wider">
            eco-respect
          </h2>
          <div className="relative group">
            <Image
              alt="Pouring matcha into a glass"
              className="w-full h-auto"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuAFBEeQQh8W-wb5sDj4oFJ07NsXGRPai2HwwLtNlvHZEqCtOrfKxzXkcbTZTEGE6fVOnERd4l7QTdeO38KDMpG6QnDiNzfZiuAekD8rMRPi2zSMth7c5GA_KdAOH8LvwTiDD0fLiolQ75i4aEjHkoCXJg5G2mblE8ngoLirT2pGuYyCo7O5Zqh-VRIjQ-7E7KJCEdTiFEK5MtIyDwDUOB_XfCd3mLtkin-8UTJveHY9f-sLfaOA2HdtPE5ypiAtCcqLdtnoeu0LDB0"
              width={1920}
              height={1080}
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex space-x-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <button className="bg-white p-3 rounded-full shadow-lg transition-transform hover:scale-110">
                <svg
                  className="h-6 w-6 text-gray-800"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15 19l-7-7 7-7"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                  ></path>
                </svg>
              </button>
              <button className="bg-white p-3 rounded-full shadow-lg transition-transform hover:scale-110">
                <svg
                  className="h-6 w-6 text-gray-800"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9 5l7 7-7 7"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
          <div className="mt-12 flex justify-center space-x-4">
            <button className="text-xs font-bold py-2 px-4 rounded-full bg-lime-300 text-black transition-all duration-300 hover:bg-lime-400 hover:shadow-md hover:-translate-y-1">
              FRESH MATCHA
            </button>
            <button className="text-xs font-bold py-2 px-4 rounded-full border border-gray-400 text-gray-600 transition-all duration-300 hover:bg-gray-100 hover:shadow-md hover:-translate-y-1">
              MATCHA PRODUCT
            </button>
            <button className="text-xs font-bold py-2 px-4 rounded-full border border-gray-400 text-gray-600 transition-all duration-300 hover:bg-gray-100 hover:shadow-md hover:-translate-y-1">
              PRODUCT KITS
            </button>
          </div>
          <div className="mt-8 max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-4">
            <Image
              alt="Person drinking matcha with a straw"
              className="w-full h-auto rounded-lg transition-transform duration-300 hover:scale-105 hover:shadow-xl"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuCb1cT53LqLeK19zM6NjR-jsVjfD_YxHTXEZ01S86DkKlnvPVW9_4qeLqCtQQT-6zRyFIWhVyVEQqbWMXQZ0kigGgPOvEZyE9OK_VEpkysLYsfbdGvHxae12szAgch9SYPpoUHE8287mdE5Rc44Oodz8bIxWdZJVGU0ITp_qBx97pMuyeiblMuFsdMdO4Tob2D8vXW9x0U-MyNPlGcgIhIKKr8Kpa8Z2w93EkXIPmWOgiivoEw7GD5hSAVlZNAELZOf99UxTUd6UMg"
              width={500}
              height={500}
            />
            <Image
              alt="Olyncha matcha in a bowl"
              className="w-full h-auto rounded-lg transition-transform duration-300 hover:scale-105 hover:shadow-xl"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuCNdCNhM_LDyAGJt1iEfRfAmQJuv92uoT4GoxMNFmRNwqyFs-rAZ9Y5igfnKC4UpnXbSBYUTaLveVB8_OaFflvueSoR5bi3cpzYPQq8dp_TLmQsp5HVReQGObxeI0xli6F2Tcaxo9EgT0FcvntRO5XnwooXvz5-0yDtsvsh8D_yBz2hLNte0cKZevo6Y70kaMIjLXtTQFkK7WzT70VvStKRjxKBZgHQSHd11ET_KS14_qvo4tR6oWkVIgiuVJpF22qJqFaLRjoUs48"
              width={500}
              height={500}
            />
            <Image
              alt="Matcha product kit"
              className="w-full h-auto rounded-lg transition-transform duration-300 hover:scale-105 hover:shadow-xl"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuCwqGTRmUpmDJYOVkJGW-pZhqFYO1rodpkJzOzMIwFeCMscpKku609EPE2l5bt1XI16Dv6EobteuFcgKKBqDDUC12i0FmTcMp6-JRiGgE7ztXS5BXoen28KgHbXjsIkxliYgGEeuN2dZ66hbfP_swygN94yl3wvckhOgz9qXdkJUHwvwFzDJyJy_Q1_fjqfkQMSFI0osIdn-fpY9ryKq67AA1H6N1XXKD8UAcEq4-WKbB9cY_hEoFs8qf8fucB4CP1JMRy2uSpgrek"
              width={500}
              height={500}
            />
          </div>
        </section>
      </main>
    </div>
  );
}
